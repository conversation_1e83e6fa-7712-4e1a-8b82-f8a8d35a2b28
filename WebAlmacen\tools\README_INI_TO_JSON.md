# Herramienta: ini_to_json.js (Multi-secciones)

Esta utilidad convierte automáticamente **todas las secciones** del archivo `MALASA.ini` (proyecto Delphi original) en un único archivo JSON llamado `fp_configuracion.json`, que puede ser utilizado por el frontend para parametrizar cualquier aspecto de la aplicación.

## ¿Por qué?
El cliente solo editará el `.ini` clásico. Esta herramienta permite que cualquier cambio en el `.ini` se refleje en la configuración global del sistema web, sin intervención manual adicional ni edición de múltiples archivos.

## Funcionamiento
- **Entrada:** Lee el archivo `OriginalDelphi/INI_ORIGINAL.ini`.
- **Salida:** Genera o actualiza el archivo `fp_configuracion.json` en la raíz del proyecto.
- **Todas** las secciones del `.ini` se exportan como propiedades del objeto JSON.

### Eje<PERSON><PERSON> de salida
```json
{
  "CONFAPP": { ... },
  "FP1_SCROLLBOX_ARTICULO": { ... },
  "TECLAS_FUNCION": { ... },
  ...
}
```

## Uso
1. Modifica el archivo `INI_ORIGINAL.ini` normalmente.
2. Ejecuta la herramienta desde la raíz del proyecto:
   ```bash
   node tools/ini_to_json.js
   ```
3. El archivo `fp_configuracion.json` se actualizará automáticamente.
4. El frontend debe leer la sección que necesite, por ejemplo:
   - Para la configuración visual: `config.FP1_SCROLLBOX_ARTICULO`
   - Para otras funcionalidades: `config.CONFAPP`, `config.TECLAS_FUNCION`, etc.

## Adaptación en el frontend
- El hook o función que lee la configuración debe cargar `fp_configuracion.json` y acceder a la sección deseada.
- Ejemplo:
  ```js
  // config = JSON.parse(...)
  const scrollboxConfig = config.FP1_SCROLLBOX_ARTICULO;
  ```

## Notas
- Si el `.ini` no tiene secciones válidas, la herramienta lo informará por consola.
- Puedes adaptar la ruta del `.ini` o `.json` editando el script.

## Requisitos
- Node.js instalado en el sistema.

---

**Este flujo centraliza toda la configuración y facilita la escalabilidad futura del sistema.**
