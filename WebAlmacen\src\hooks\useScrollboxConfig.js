import { useEffect, useState } from 'react';

/**
 * Hook para obtener la configuración visual del scrollbox de artículos desde el backend
 * @returns {object|null} config
 */
/**
 * Hook para obtener la configuración global del sistema desde el backend
 * @param {string} section - (opcional) nombre de la sección a devolver (por ejemplo, 'FP1_SCROLLBOX_ARTICULO')
 * @returns {object} { config, sectionConfig, error }
 */
export default function useScrollboxConfig(section) {
  const [config, setConfig] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetch('/fp_configuracion.json')
      .then(res => {
        if (!res.ok) throw new Error('No se pudo cargar la configuración');
        return res.json();
      })
      .then(setConfig)
      .catch(err => setError(err));
  }, []);

  const sectionConfig = config && section ? config[section] : undefined;
  return { config, sectionConfig, error };
}
