# Guía: ¿Cómo cambiar valores del INI y verlos reflejados en la web?

Este proyecto permite que la configuración visual y funcional de la aplicación web se controle fácilmente editando un archivo INI clásico. Sigue estos pasos para que tus cambios en el INI se reflejen correctamente en la web.

---

## 1. Edita el archivo INI original

- Ubicación: `OriginalDelphi/INI_ORIGINAL.ini`
- Abre el archivo con tu editor favorito.
- Modifica, agrega o elimina cualquier valor, sección o clave según lo que necesites parametrizar.
  - Ejemplo:
    ```ini
    [FP1_SCROLLBOX_ARTICULO]
    lbdescripcion1_left=20
    lbdescripcion1_linea=2
    mensaje_instruccion1=FP02->Introduzca ubicación
    mensaje_instruccion2=FP02->Lea CB del artículo ó selecciónelo de la pantalla
    ```

## 2. Ejecuta la herramienta de conversión INI → JSON

Desde la raíz del proyecto, ejecuta:
```bash
node tools/ini_to_json.js
```
Esto generará o actualizará el archivo `fp_configuracion.json` con todas las secciones y claves del INI, listas para ser usadas por el frontend.

## 3. Refresca la web en tu navegador

- No es necesario reiniciar el servidor de desarrollo.
- Simplemente recarga la página (F5 o Ctrl+R) en tu navegador.
- Los cambios del INI ya estarán reflejados en la aplicación web.

## 4. (Opcional) Verifica el JSON generado

- Puedes abrir `fp_configuracion.json` para comprobar que tus cambios del INI se exportaron correctamente.
- El frontend leerá directamente de este archivo.

---

## Resumen del flujo
1. **Edita** el INI (`INI_ORIGINAL.ini`).
2. **Ejecuta** la herramienta de conversión:
   ```
   node tools/ini_to_json.js
   ```
3. **Refresca** la web.

¡Listo! Así de sencillo es mantener la configuración de la app web sincronizada con el archivo INI clásico.

---

**¿Dudas o problemas?**
- Si algún cambio no se refleja, asegúrate de que el INI esté bien escrito y que ejecutaste correctamente la herramienta.
- Si el frontend no muestra los cambios, revisa la consola del navegador por posibles errores de sintaxis en el JSON.
