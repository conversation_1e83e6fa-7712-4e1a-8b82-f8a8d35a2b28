# Personalización Avanzada del Scrollbox de Artículos (FP1)

Este documento detalla de manera exhaustiva el uso, propósito y personalización de la sección `[FP1_SCROLLBOX_ARTICULO]` en el archivo de configuración `MALASA.ini` de la aplicación. Aquí aprenderás cómo cada parámetro afecta la interfaz, cómo se utiliza en el código fuente Delphi, y las mejores prácticas para adaptar la experiencia visual a tus necesidades.

---

## ¿Qué es `[FP1_SCROLLBOX_ARTICULO]`?

Es una sección del archivo INI que permite controlar de forma granular la disposición, tamaño, fuente y posición de cada etiqueta y campo en el scrollbox de artículos de la pantalla FP1. Su objetivo es que puedas adaptar la interfaz gráfica sin tocar el código fuente, solo editando el archivo de configuración.

---

## Ejemplo completo de sección INI

```ini
[FP1_SCROLLBOX_ARTICULO]
HEIGHT=87
HEIGHT_RESUMIDO=50
TOPLINEA1=1
TOPLINEA2=15
TOPLINEA3=29
TOPLINEA4=43
TOPLINEA5=56
lbSSCC_left=150
lbSSCC_linea=56
lbSSCC_font=8
lbTipoPedido_left=150
lbTipoPedido_linea=56
lbTipoPedido_font=8
lbLetraEstado_left=150
lbLetraEstado_linea=56
lbLetraEstado_font=8
lbLetraEstado_click=#1
lbenvase_left=150
lbenvase_linea=56
lbenvase_font=8
lbdescripcion1_Left=3
lbdescripcion1_linea=15
lbdescripcion1_font=8
lbdescripcion2_Left=3
lbdescripcion2_linea=29
lbdescripcion2_font=8
lbdescripcion3_Left=1000
lbdescripcion3_linea=29
lbdescripcion3_font=8
lbdescripcion4_Left=1000
lbdescripcion4_linea=29
lbdescripcion4_font=8
lbCB_Left=3
lbCB_linea=43
lbCB_font=8
lbpeso_Left=80
lbpeso_linea=56
lbpeso_font=8
lbUDS_left=150
lbUDS_linea=1
lbUDS_font=8
lbUnidadesTotales_left=150
lbUnidadesTotales_linea=1
lbUnidadesTotales_font=8
lblote_left=3
lblote_linea=56
lblote_font=8
lbubicacionActual_left=3
lbubicacionActual_linea=43
lbubicacionActual_font=8
```

---

## Explicación de cada parámetro

- **HEIGHT / HEIGHT_RESUMIDO**: Altura total del scrollbox en modo normal y resumido. Permite adaptar el espacio vertical de la zona de artículos.
- **TOPLINEA1-5**: Posición vertical (top, en píxeles) de cada línea de información. Sirven como referencia para ubicar los distintos labels y campos.
- **lbXXX_left**: Posición horizontal (en píxeles) del label o campo correspondiente. Permite alinear cada campo de forma independiente.
- **lbXXX_linea**: Línea (posición vertical) donde se ubica el label, normalmente coincide con uno de los TOPLINEA.
- **lbXXX_font**: Tamaño de fuente del label. Ajusta la legibilidad y el impacto visual.
- **lbLetraEstado_click**: Parámetro especial, puede indicar si el label es interactivo o tiene comportamiento especial (por ejemplo, si reacciona a clics).

### Ejemplo visual

Supón que quieres que el campo "SSCC" esté más visible y alineado a la izquierda, solo cambia:
```ini
lbSSCC_left=10
lbSSCC_linea=15
lbSSCC_font=12
```

---

## ¿Cómo se usa en el código fuente Delphi?

Cada parámetro se lee y asigna así:
```delphi
FHeightScrollBox_FP1 := strtoint(IniFile.ReadString('FP1_SCROLLBOX_ARTICULO', 'HEIGHT', '87'));
FTopLinea1_FP1 := strtoint(IniFile.ReadString('FP1_SCROLLBOX_ARTICULO', 'TOPLINEA1', '1'));
FlbSSCC_left_FP1 := strtoint(IniFile.ReadString('FP1_SCROLLBOX_ARTICULO', 'lbSSCC_left', '150'));
FlbSSCC_linea_FP1 := strtoint(IniFile.ReadString('FP1_SCROLLBOX_ARTICULO', 'lbSSCC_linea', inttostr(FTopLinea5_FP1)));
FlbSSCC_font_FP1 := strtoint(IniFile.ReadString('FP1_SCROLLBOX_ARTICULO', 'lbSSCC_font', '8'));
// ...y así para cada parámetro
```
Luego, los valores se aplican a los controles visuales:
```delphi
LabelSSCC.Left := FlbSSCC_left_FP1;
LabelSSCC.Top := FlbSSCC_linea_FP1;
LabelSSCC.Font.Size := FlbSSCC_font_FP1;
```
Esto garantiza que cualquier cambio en el INI se refleje en la interfaz tras reiniciar la aplicación, sin recompilar.

---

## Mejores prácticas y recomendaciones

- **No uses líneas comentadas**: los valores deben estar activos (sin punto y coma al inicio) para que el sistema los lea y aplique.
- **Adapta los valores a tu resolución y preferencias**: prueba diferentes posiciones y tamaños para lograr la mejor experiencia visual.
- **Reinicia la aplicación después de modificar el INI** para que los cambios tengan efecto.
- **Consulta siempre el archivo de ejemplo** para no omitir ningún parámetro importante.
- **Haz backup de tu INI antes de experimentar** para poder volver atrás fácilmente.

---

## Preguntas frecuentes

**¿Qué pasa si omito un parámetro?**
El sistema usará el valor por defecto definido en el código. Sin embargo, para un control total, incluye todos los parámetros relevantes.

**¿Puedo agregar nuevos campos?**
Solo si el código fuente Delphi está preparado para leerlos y usarlos. Consulta al desarrollador antes de innovar.

**¿Por qué no veo cambios tras editar el INI?**
Asegúrate de que:
- El archivo INI está en la ruta correcta.
- Los valores no están comentados.
- Reiniciaste la aplicación.

---

## Recursos adicionales

- Archivo ejemplo: [`EJEMPLO_FP1_SCROLLBOX_ARTICULO.ini`](./EJEMPLO_FP1_SCROLLBOX_ARTICULO.ini)
- Consulta el código fuente relacionado en `uFPFormPrincipal.pas` y módulos auxiliares.
- Contacta al responsable de desarrollo para ampliaciones.

---

*Documento generado para facilitar la personalización avanzada de la interfaz FP1 en la aplicación MALASA.*
