inherited FrmAuxFP02: TFrmAuxFP02
  Caption = 'V'
  ClientWidth = 298
  StyleElements = [seFont, seClient, seBorder]
  OnShow = FormShow
  ExplicitWidth = 298
  TextHeight = 13
  object lbAyuda: TLabel [0]
    Left = 14
    Top = 73
    Width = 5
    Height = 19
    Color = clBlack
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWhite
    Font.Height = -16
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentColor = False
    ParentFont = False
  end
  inherited PanelCabecera: TPanel
    Width = 298
    Height = 61
    StyleElements = [seFont, seClient, seBorder]
    ExplicitWidth = 298
    ExplicitHeight = 61
    inherited Panel2: TPanel
      Left = 188
      Height = 61
      StyleElements = [seFont, seClient, seBorder]
      ExplicitLeft = 188
      ExplicitHeight = 61
      inherited spbpor: TImage
        Top = 41
        ExplicitTop = 41
      end
      inherited SPBmenos: TImage
        Top = 21
        ExplicitTop = 21
      end
      inherited spbmas: TImage
        Top = 1
        ExplicitTop = 1
      end
    end
    inherited PanelBTN: TPanel
      Left = 168
      Height = 61
      StyleElements = [seFont, seClient, seBorder]
      ExplicitLeft = 168
      ExplicitHeight = 61
      inherited SpbAnadir: TImage
        Top = 21
        ExplicitTop = 21
      end
      inherited spbDesglose: TImage
        Top = 1
        ExplicitTop = 1
      end
    end
    inherited PanelBLANKE: TPanel
      Height = 61
      StyleElements = [seFont, seClient, seBorder]
      ExplicitHeight = 61
    end
    inherited PanelBotonesADV: TPanel
      Left = 208
      Height = 61
      StyleElements = [seFont, seClient, seBorder]
      ExplicitLeft = 208
      ExplicitHeight = 61
      inherited PanelADVBOTONESA: TPanel
        Height = 61
        StyleElements = [seFont, seClient, seBorder]
        ExplicitHeight = 61
      end
      inherited PanelADVBOTONESB: TPanel
        Height = 61
        StyleElements = [seFont, seClient, seBorder]
        ExplicitHeight = 61
      end
    end
  end
  inherited PanelFooter: TPanel
    Width = 298
    TabOrder = 2
    StyleElements = [seFont, seClient, seBorder]
    ExplicitWidth = 298
    inherited Panel3: TPanel
      Left = 210
      StyleElements = [seFont, seClient, seBorder]
      ExplicitLeft = 210
    end
    inherited Panel5: TPanel
      StyleElements = [seFont, seClient, seBorder]
    end
    inherited Panel6: TPanel
      Left = 230
      StyleElements = [seFont, seClient, seBorder]
      ExplicitLeft = 230
    end
    inherited BTnfooterRigrh: TAdvShapeButton
      Left = 285
      Top = 6
      OnClick = BTnfooterLEftClick
      ExplicitLeft = 285
      ExplicitTop = 6
    end
    inherited BTnfooterCenter: TAdvShapeButton
      Left = 133
      Top = 6
      OnClick = BTnfooterCenterClick
      ExplicitLeft = 133
      ExplicitTop = 6
    end
  end
  object panelDetalle: TPanel
    Left = 0
    Top = 91
    Width = 298
    Height = 261
    Align = alClient
    BevelOuter = bvNone
    Color = clBlack
    ParentBackground = False
    TabOrder = 1
    object pnlCabeceraDetalle: TPanel
      Left = 0
      Top = 0
      Width = 298
      Height = 22
      Align = alTop
      Color = clBlack
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWhite
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentBackground = False
      ParentFont = False
      TabOrder = 0
      DesignSize = (
        298
        22)
      object Label2: TLabel
        Left = 119
        Top = 3
        Width = 28
        Height = 16
        Caption = 'Ubic.'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object BTNBotonDetalle: TImage
        Left = 266
        Top = 1
        Width = 31
        Height = 20
        Hint = 'A'#241'adir Documento'
        Align = alRight
        Picture.Data = {
          07544269746D6170E6040000424DE60400000000000036000000280000001400
          0000140000000100180000000000B0040000C20E0000C20E0000000000000000
          00000000000000000000000000000000000A0A0A474747939393C8C8C8E2E2E2
          E3E3E3CACACA9898984C4C4C0C0C0C0000000000000000000000000000000000
          00000000000000000000353535AAAAAAEFEFEFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFF2F2F2B1B1B13E3E3E000000000000000000000000000000000000
          0101015A5A5AE2E2E2FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFE8E8E8666666030303000000000000000000000000585858EE
          EEEEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFF3F3F36565650000000000000000002C2C2CDDDDDDFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFE5E5E5373737000000040404989898FFFFFFFFFFFFFFFFFFFAFAFA
          EFEFEFEFEFEFEFEFEFEFEFEFEFEFEFEFEFEFEFEFEFEFEFEFF6F6F6FFFFFFFFFF
          FFFFFFFFA6A6A6080808313131E4E4E4FFFFFFFFFFFFEAEAEA6B6B6B5F5F5F67
          67676666666666666666666666666767676161614F4F4FC3C3C3FFFFFFFFFFFF
          ECECEC3E3E3E757575FDFDFDFFFFFFFFFFFFBEBEBE676767E6E6E6E9E9E9E8E8
          E8E8E8E8E8E8E8E8E8E8E8E8E8E8E8E8666666747474FFFFFFFFFFFFFFFFFF86
          8686A7A7A7FFFFFFFFFFFFFFFFFFBBBBBB8E8E8EFFFFFFFEFEFEFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFF909090737373FFFFFFFFFFFFFFFFFFB9B9B9C1C1
          C1FFFFFFFFFFFFFFFFFFBBBBBB8D8D8DFFFFFFFEFEFEFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFF8F8F8F737373FFFFFFFFFFFFFFFFFFD1D1D1C0C0C0FFFFFF
          FFFFFFFFFFFFBBBBBB8D8D8DFFFFFFFEFEFEFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFF8F8F8F737373FFFFFFFFFFFFFFFFFFD2D2D2A6A6A6FFFFFFFFFFFFFF
          FFFFBBBBBB8D8D8DFFFFFFFEFEFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          8F8F8F737373FFFFFFFFFFFFFFFFFFB9B9B9727272FCFCFCFFFFFFFFFFFFBABA
          BA8C8C8CFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8F8F8F72
          7272FFFFFFFFFFFFFFFFFF868686303030E4E4E4FFFFFFFFFFFFC3C3C3525252
          BBBBBBC3C3C3C2C2C2C2C2C2C2C2C2C2C2C2C3C3C3BEBEBE4B4B4B808080FFFF
          FFFFFFFFECECEC3E3E3E030303989898FFFFFFFFFFFFF6F6F69999997676767A
          7A7A7A7A7A7A7A7A7A7A7A7A7A7A7A7A7A7777777E7E7EE0E0E0FFFFFFFFFFFF
          A6A6A60808080000002B2B2BDBDBDBFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE5E5E537373700
          0000000000000000555555ECECECFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2F2F26363630000000000000000
          00000000000000595959E2E2E2FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFFFFFFFFFFFFFFFFFFFFFE8E8E8646464020202000000000000000000000000
          000000000000343434A9A9A9EFEFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
          FFF1F1F1B0B0B03C3C3C00000000000000000000000000000000000000000000
          0000000000090909454545929292C7C7C7E1E1E1E2E2E2CACACA9797974B4B4B
          0C0C0C000000000000000000000000000000}
        OnClick = BTNBotonDetalleClick
        ExplicitLeft = 272
        ExplicitTop = -1
      end
      object cbDocumento1: TComboBox
        Left = 1
        Top = 1
        Width = 81
        Height = 21
        Align = alLeft
        Style = csDropDownList
        Color = clBlack
        DropDownCount = 6
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        OnChange = cbDocumento1Change
        Items.Strings = (
          ''
          'Entradas'
          'Internas')
      end
      object adVBTNBotonDetalle: TAdvShapeButton
        Left = 200
        Top = 0
        Width = 54
        Height = 19
        Appearance.Shape = bsRectangle
        Appearance.BorderColor = clBlack
        Appearance.BorderColorHot = clBlack
        Appearance.BorderColorDown = clBlack
        Appearance.BorderColorDisabled = clBlack
        Appearance.InnerBorderColor = clBlack
        Appearance.InnerBorderColorHot = clBlack
        Appearance.InnerBorderColorDown = clBlack
        Appearance.InnerBorderColorDisabled = clBlack
        Appearance.Color = clBlack
        Appearance.ColorTo = clBlack
        Appearance.ColorMirror = clBlack
        Appearance.ColorMirrorTo = clBlack
        Appearance.ColorHot = 16744448
        Appearance.ColorHotTo = 16744448
        Appearance.ColorHotMirror = 16744448
        Appearance.ColorHotMirrorTo = 16744448
        Appearance.ColorDown = 16744448
        Appearance.ColorDownTo = 16744448
        Appearance.ColorDownMirror = 16744448
        Appearance.ColorDownMirrorTo = 16744448
        Appearance.ColorDisabled = clBlack
        Appearance.ColorDisabledTo = clBlack
        Appearance.ColorDisabledMirror = clBlack
        Appearance.ColorDisabledMirrorTo = clBlack
        Appearance.MenuShapeColor = clBlack
        Appearance.MenuShapeColorHot = clBlack
        Appearance.MenuShapeColorDown = clBlack
        Appearance.MenuShapeColorDisabled = clBlack
        Anchors = [akRight]
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = plPictureOnTop
        Picture.Data = {
          89504E470D0A1A0A0000000D494844520000002F0000002F0806000000732793
          AC000000017352474200AECE1CE90000000467414D410000B18F0BFC61050000
          00097048597300000EC400000EC401952B0E1B0000055C494441546843D59A4B
          2C5C5F1CC78FDBAAB7444810998660D808362442241A41246DAA5D782D9A1089
          57041112D2B0608FAE2C2A1A0BB16942A26C344D2B939685D7D036528F2233C4
          AB5EADD2D3F33BFDDDFBBFD78C993BB753E6FF494ECEEFBC7EE73BD73DF7BCB8
          51067102A7A7A7C4603090F9F979B2BABA4A0E0E0EC8CF9F3F892008C4D7D797
          84868692D8D858929C9C4CC2C2C2B0D55F02E2B5323333439F3C79423D3C3CE0
          013814D2D3D3E9E0E0207AD28626F17D7D7D943D4DABA2C470E7CE1D5EC7CBCB
          CB6AB93C545555A167C77048FCF8F8381775B9739D4E479B9A9AE8DBB76FE9C9
          C909D6B664717191767777D3B4B4340B1F109A9B9BB1A63A548BCFC8C8B0E8AC
          A1A1811E1F1F630DC779F1E2050D0C0C54F8747777A7CBCBCB58C33676C59BCD
          668573088E3E217B8C8D8D59F4D1D3D383A5576353FC870F1F140EA3A2A228FB
          8260A9F3A9ACAC54F45751518125D6B952FC65E1D5D5D558F26F817123EFB7A4
          A4044B2CB12AFEEBD7AF0A076AFE84CE64777757D17F6B6B2B9628B12A5EDEF0
          BA858B7CFBF64DA1E3CD9B3758F21F16E2D90C2835A8ABABC3DC9BE1F3E7CF8A
          1F701945CEF0F0B05451AFD763EECDD2D9D92969BA77EF1EE6FE41215EAC04E1
          D7AF5F987BF3C4C4C448BA666767315726FED9B3675285B6B636CC55070CB0FD
          FD7DD5616F6F8F1E1E1E626BFB6C6D6D49DAA2A3A33157265E2C84A096DCDC5C
          453B2D61727212BDD9E6FEFDFB529B2F5FBEF03CAEF4DDBB775281DAD9B3B8B8
          98D79F9898A0474747FC69AA0DF0F4D99299E6E7E7731F6AFE0A269349D208ED
          002EFEE1C3875281DA1914EA0E0C0C604A3BE0E7E9D3A798B24D7878B8A41310
          98415EBE7C091161D33FB97DFB36B7D5C09CA1A51DF0B1BDBD8D29DBB0591E2D
          423E7EFC480436D83049485151115AEA383F3F474B3B1717177CB7A586828202
          B408191D1D25027B673149487676365AD78B9B9B1B5AB681ADA4086C39858585
          054C129290908096EB02AF3660341A89B0B2B2C21380A7A7275AAE0B9BB078BC
          B9B9490471B0DCBA758BC7AE4E4848088FD97693086767673CF17F11CFB6893C
          E6035D7C559CF1E5B80E7EFCF8C16378D8427070304FB085188F5D1D78D7011F
          1F1F22C8271A36CDA3E5BAC0E404E8743A22C4C5C5F1043035358596EB024789
          00E816525252780278F5EA155AAEC9F2F2325A84806E010E41C511DCDFDFCF63
          B538E30B053ED48E37B9BE9C9C1CC217156C89C9336030C0E9AE5ACC66335ADA
          8149D2DFDF1F53B6E9EAEA420B1785B0B484D35E96E6010E7ED4206E44C48D81
          166A6B6BB90FD829D943BE192F2B2BE379D2B6C9CFCF4F2A540B5B0B496DB486
          919111F4669BD4D454A9CDCECE0ECF932E17D8C6425A72B2A74FD89E96DBF660
          BB20FE0550BBAC05A04B18677ABD1E736CF3E9D3277E310124252591F7EFDF73
          5BF198BDBDBDA55F079B6A57417E920CA779220AF1F2F3C9808000CCBD59EAEB
          EB254D85858598FB078B175CBE9F7DF4E811E6DE0CAF5FBF96B440B88C650E43
          FEFA34363662EEF5C2DE738570F8DA5CC6AAF8EFDFBF2B1AC20DC875323737A7
          E81FEEC0AC61553CB0BEBEAE709095958525FF96E7CF9F2BFAEDE8E8C0124BAE
          140F6C6C6C281CB1B53F5D5A5AC252E7939999A9E80F0E596D6153BC48505090
          C2695E5E1E659B022CFD7BE427C16280C16A0F55E281F2F2728B0E1E3C78C097
          165A802BCF9696160B9F111111AA1F8C6AF1C0DADA1A8D8C8CB4E810C2E3C78F
          696F6F2F351A8D568F0C610C0D0D0DD19A9A1ACA766F567DB05523D6568743E2
          450C06038D8F8FB72AC0D10097D270B1AC054DE245E002B9BDBD9D9F995B1376
          55806BFDD2D2523A3D3D8D9EB4E1B4FFFA0060A7C3BED13C36994CFC6C051660
          6CC093BB77EFF2C5556262A2EAE33DDB10F21BDA3FF4BB519C72620000000049
          454E44AE426082}
        ParentBackground = False
        ParentFont = False
        TabOrder = 1
        Version = '6.2.1.8'
        Visible = False
        OnClick = BTNBotonDetalleClick
      end
      object ComboAgrupado: TComboBox
        Left = 216
        Top = 1
        Width = 50
        Height = 21
        Align = alRight
        Style = csDropDownList
        Color = clBlack
        DropDownCount = 6
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 2
        OnChange = ComboAgrupadoChange
        Items.Strings = (
          ''
          'Entradas'
          'Internas')
      end
      object ADVPdteRecibir_DOS: TAdvShapeButton
        Left = 88
        Top = -2
        Width = 25
        Height = 21
        Appearance.Shape = bsRectangle
        Appearance.BorderColor = 10569759
        Appearance.BorderColorDown = 10569759
        Appearance.InnerBorderColor = 15042372
        Appearance.InnerBorderColorHot = 15966549
        Appearance.InnerBorderColorDown = 15042372
        Appearance.Color = 14318910
        Appearance.ColorTo = 11690024
        Appearance.ColorHot = 14515779
        Appearance.ColorHotTo = 12347186
        Appearance.ColorDown = 5496778
        Appearance.ColorDownTo = 11690024
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentBackground = False
        ParentFont = False
        TabOrder = 3
        Version = '6.2.1.8'
        OnClick = ADVPdteRecibir_DOSClick
      end
    end
    object dbgListadoOrdenesUbicacion: TDBAdvGrid
      AlignWithMargins = True
      Left = 3
      Top = 84
      Width = 292
      Height = 174
      Align = alClient
      ColCount = 1
      DefaultRowHeight = 17
      DrawingStyle = gdsClassic
      FixedColor = clBlack
      FixedCols = 0
      RowCount = 2
      FixedRows = 1
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -12
      Font.Name = 'Tahoma'
      Font.Style = []
      GradientEndColor = clNone
      GradientStartColor = clBlack
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goColSizing, goRowSelect, goFixedColClick, goFixedRowClick]
      ParentFont = False
      TabOrder = 1
      Touch.GestureManager = GestureManager1
      OnClick = dbgListadoOrdenesUbicacionClick
      OnDblClick = dbgListadoOrdenesUbicacionDblClick
      OnGesture = dbgListadoOrdenesUbicacionGesture
      OnGetCellColor = dbgListadoOrdenesUbicacionGetCellColor
      OnClickSort = dbgListadoOrdenesUbicacionClickSort
      ActiveCellFont.Charset = DEFAULT_CHARSET
      ActiveCellFont.Color = clWindowText
      ActiveCellFont.Height = -11
      ActiveCellFont.Name = 'Tahoma'
      ActiveCellFont.Style = [fsBold]
      BorderColor = clBlack
      ColumnSize.Save = True
      ControlLook.FixedGradientHoverFrom = clGray
      ControlLook.FixedGradientHoverTo = clWhite
      ControlLook.FixedGradientDownFrom = clGray
      ControlLook.FixedGradientDownTo = clSilver
      ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownHeader.Font.Color = clWindowText
      ControlLook.DropDownHeader.Font.Height = -11
      ControlLook.DropDownHeader.Font.Name = 'Tahoma'
      ControlLook.DropDownHeader.Font.Style = []
      ControlLook.DropDownHeader.Visible = True
      ControlLook.DropDownHeader.Buttons = <>
      ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownFooter.Font.Color = clWindowText
      ControlLook.DropDownFooter.Font.Height = -11
      ControlLook.DropDownFooter.Font.Name = 'Tahoma'
      ControlLook.DropDownFooter.Font.Style = []
      ControlLook.DropDownFooter.Visible = True
      ControlLook.DropDownFooter.Buttons = <>
      ControlLook.ToggleSwitch.BackgroundBorderWidth = 1.000000000000000000
      ControlLook.ToggleSwitch.ButtonBorderWidth = 1.000000000000000000
      ControlLook.ToggleSwitch.CaptionFont.Charset = DEFAULT_CHARSET
      ControlLook.ToggleSwitch.CaptionFont.Color = clWindowText
      ControlLook.ToggleSwitch.CaptionFont.Height = -18
      ControlLook.ToggleSwitch.CaptionFont.Name = 'Segoe UI'
      ControlLook.ToggleSwitch.CaptionFont.Style = []
      ControlLook.ToggleSwitch.Shadow = False
      EnhTextSize = True
      Filter = <>
      FilterDropDown.Font.Charset = DEFAULT_CHARSET
      FilterDropDown.Font.Color = clWindowText
      FilterDropDown.Font.Height = -11
      FilterDropDown.Font.Name = 'Tahoma'
      FilterDropDown.Font.Style = []
      FilterDropDown.TextChecked = 'Checked'
      FilterDropDown.TextUnChecked = 'Unchecked'
      FilterDropDownClear = '(All)'
      FilterEdit.TypeNames.Strings = (
        'Starts with'
        'Ends with'
        'Contains'
        'Not contains'
        'Equal'
        'Not equal'
        'Larger than'
        'Smaller than'
        'Clear')
      FixedAsButtons = True
      FixedColWidth = 30
      FixedRowHeight = 17
      FixedFont.Charset = DEFAULT_CHARSET
      FixedFont.Color = clWindowText
      FixedFont.Height = -11
      FixedFont.Name = 'Tahoma'
      FixedFont.Style = [fsBold]
      FloatFormat = '%.2f'
      HoverButtons.Buttons = <>
      HTMLSettings.ImageFolder = 'images'
      HTMLSettings.ImageBaseName = 'img'
      MouseActions.AllRowSize = True
      PrintSettings.DateFormat = 'dd/mm/yyyy'
      PrintSettings.Font.Charset = DEFAULT_CHARSET
      PrintSettings.Font.Color = clWindowText
      PrintSettings.Font.Height = -11
      PrintSettings.Font.Name = 'Tahoma'
      PrintSettings.Font.Style = []
      PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
      PrintSettings.FixedFont.Color = clWindowText
      PrintSettings.FixedFont.Height = -11
      PrintSettings.FixedFont.Name = 'Tahoma'
      PrintSettings.FixedFont.Style = []
      PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
      PrintSettings.HeaderFont.Color = clWindowText
      PrintSettings.HeaderFont.Height = -11
      PrintSettings.HeaderFont.Name = 'Tahoma'
      PrintSettings.HeaderFont.Style = []
      PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
      PrintSettings.FooterFont.Color = clWindowText
      PrintSettings.FooterFont.Height = -11
      PrintSettings.FooterFont.Name = 'Tahoma'
      PrintSettings.FooterFont.Style = []
      PrintSettings.PageNumSep = '/'
      ScrollProportional = True
      ScrollWidth = 21
      SearchFooter.ColorTo = clWhite
      SearchFooter.FindNextCaption = 'Find &next'
      SearchFooter.FindPrevCaption = 'Find &previous'
      SearchFooter.Font.Charset = DEFAULT_CHARSET
      SearchFooter.Font.Color = clWindowText
      SearchFooter.Font.Height = -11
      SearchFooter.Font.Name = 'Tahoma'
      SearchFooter.Font.Style = []
      SearchFooter.HighLightCaption = 'Highlight'
      SearchFooter.HintClose = 'Close'
      SearchFooter.HintFindNext = 'Find next occurrence'
      SearchFooter.HintFindPrev = 'Find previous occurrence'
      SearchFooter.HintHighlight = 'Highlight occurrences'
      SearchFooter.MatchCaseCaption = 'Match case'
      SearchFooter.ResultFormat = '(%d of %d)'
      SelectionTextColor = clWindowText
      Version = '2.5.1.23'
      AutoCreateColumns = True
      AutoRemoveColumns = True
      Columns = <
        item
          Borders = []
          BorderPen.Color = clSilver
          ButtonHeight = 18
          CheckFalse = 'N'
          CheckTrue = 'Y'
          Color = clWindow
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          HeaderFont.Charset = DEFAULT_CHARSET
          HeaderFont.Color = clWindowText
          HeaderFont.Height = -11
          HeaderFont.Name = 'Tahoma'
          HeaderFont.Style = []
          PrintBorders = [cbTop, cbLeft, cbRight, cbBottom]
          PrintFont.Charset = DEFAULT_CHARSET
          PrintFont.Color = clWindowText
          PrintFont.Height = -11
          PrintFont.Name = 'Tahoma'
          PrintFont.Style = []
          Width = 30
        end>
      DataSource = dtsListadoOrdenesUbicacion
      PageMode = False
      InvalidPicture.Data = {
        055449636F6E0000010001002020200000000000A81000001600000028000000
        2000000040000000010020000000000000100000000000000000000000000000
        0000000000000000000000000000000000000000000000000000000000000000
        0000000000000000000000006A6A6B256A6A6B606A6A6B946A6A6BC06A6A6BE1
        6A6A6BF86A6A6BF86A6A6BE16A6A6BC06A6A6B946A6A6B606A6A6B2500000000
        0000000000000000000000000000000000000000000000000000000000000000
        0000000000000000000000000000000000000000000000000000000000000000
        000000006A6A6B407575769E787879F19F9F9FF6C0C0C0FDDADADAFFEDEDEEFF
        FBFBFBFFFBFBFBFFEDEDEEFFDADADAFFC0C0C0FD9F9F9FF6787879F17575769E
        6A6A6B4000000000000000000000000000000000000000000000000000000000
        000000000000000000000000000000000000000000000000000000006A6A6B22
        7C7C7C98888889F0BDBDBDFCE9E9EBFED9D9E9FEB5B5DDFE8B8BCDFE595AB7FF
        3739A8FF2B2CA4FF4A49B1FF7171C1FFA1A2D7FFD3D3E8FFEAEAEBFEBEBEBFFC
        888889F07C7C7C986A6A6B220000000000000000000000000000000000000000
        0000000000000000000000000000000000000000000000006A6A6B43838383D8
        B7B7B8FAECECEFFEC0C0DFFF7977C4FF2221A0FF12129BFF1010A4FF0C0CA8FF
        0A0AACFF0A0AB4FF0A0AB9FF0D0DBEFF0F0FB1FF1111A6FF5656B8FFAEADDCFF
        ECECEFFEB7B7B8FA838383D86A6A6B4300000000000000000000000000000000
        00000000000000000000000000000000000000006A6A6B4E878788EAD3D3D3FE
        CACAE8FF4443B0FF171799FF11119CFF0C0C98FF0B0B9BFF0B0BA0FF0A0AA6FF
        0909ACFF0909B2FF0808BAFF0707BFFF0B09C8FF0D0DCEFF1111CCFF1010AFFF
        4A49B2FFCFCFEBFFD3D3D3FE878788EA6A6A6B4E000000000000000000000000
        000000000000000000000000000000006A6A6B43878788EAE1E1E1FFA8A8DAFF
        2323A0FF15159CFF0D0D92FF0C0C95FF0C0C99FF0B0B9EFF0B0BA0FF0A0AA6FF
        0909ACFF0909B2FF0808B8FF0808BCFF0808C3FF0C0CC9FF0C0CD0FF0D0DD6FF
        1313CFFF2222A9FFAFAFDEFFE1E1E1FF878788EA6A6A6B430000000000000000
        0000000000000000000000006A6A6B22838383D8D3D3D3FEA8A8D9FF2020A4FF
        13139BFF0C0C92FF0C0C95FF0C0C97FF0C0C99FF0B0B9EFF0B0BA0FF0A0AA4FF
        0A0AA9FF0909B0FF0808B4FF0808BBFF0707C0FF0A0AC6FF0909CCFF0C0CD3FF
        0D0DD8FF1313D3FF1A1AA8FFAEADDEFFD4D4D4FE838383D86A6A6B2200000000
        0000000000000000000000007C7C7C98B7B7B8FACACAE8FF2524A3FF13139FFF
        0C0C97FF0C0C95FF0C0C95FF0C0C91FF0C0C95FF0B0B9EFF0B0BA0FF0A0AA4FF
        0A0AA8FF0909ADFF0909B2FF0808B8FF0808BCFF0707C0FF0808BCFF0707C5FF
        0C0CD3FF0D0DD7FF1212D1FF2020A7FFCDCDEBFFB8B8B9FA7C7C7C9800000000
        00000000000000006A6A6B40888889F0ECECEFFE4545B1FF1616A4FF0B0B9BFF
        0C0C99FF0C0C96FF3333A2FFB9B9D0FF393A9BFF0C0C95FF0B0BA1FF0A0AA4FF
        0A0AA7FF0A0AABFF0909B0FF0808B4FF0808B7FF2F2FC2FFAEAEE2FF4B4BBFFF
        0707BEFF0B0BD1FF0C0CD3FF1413CCFF4848B1FFECECEFFE888889F06A6A6B40
        00000000000000007575769EBFBFBFFD9B9BD5FF1C1CA6FF0C0CA1FF0B0B9FFF
        0B0B9AFF3535A7FFB5B5BEFFE6E6DFFFEDEDEFFF3C3C9CFF0C0C97FF0A0AA4FF
        0A0AA6FF0A0AA9FF0909ADFF0909B0FF2626B5FFCECEDEFFFFFFFBFFEEEEF1FF
        4848BAFF0808BCFF0A0ACDFF0B0BCEFF1111ABFFBEC0E0FFBFC0BFFD7575769E
        000000006A6A6B25787879F1E3E3E5FE4646B2FF1414A8FF0A0AA4FF0B0BA0FF
        2121A9FFBDBDCAFFD0D0C8FFC5C5C5FFE3E3E1FFEDEDEFFF3E3E9EFF0C0C98FF
        0A0AA6FF0A0AA8FF0A0AA9FF2B2BB0FFC0C0CDFFEAEAE2FFEBEBEBFFFEFEF8FF
        EDEDEEFF2828BDFF0707C4FF0809C7FF0F0FC4FF8788CBFFEBEBECFE79797AF1
        6A6A6B256A6A6B609D9E9DF6D6D7E4FF3A3AB3FF1212ADFF0A0AA8FF0A0AA4FF
        1313AAFFABABCFFFD6D6CBFFCACACAFFC6C6C6FFE4E4E0FFEEEEEFFF3F3FA0FF
        0C0C99FF0A0AA6FF2828ABFFB2B2BFFFD8D8CEFFD6D6D8FFE0E0E0FFF6F5EDFF
        D1D1EDFF1E1CC0FF0707BEFF0707BFFF0707C0FF2120AAFFD3D5E9FE9FA0A0F6
        6A6A6B606A6A6B94BDBDBDFBBABBDCFF3A39B7FF2F2FB8FF0909ADFF0A0AA9FF
        0A0AA6FF1515ACFFADADCFFFD6D6CBFFCBCBCAFFC6C6C6FFE4E4E1FFEEEEEFFF
        3838A1FF2222A2FFACABB8FFC8C8C0FFC7C7C8FFCDCDCDFFE1E1D9FFC8CAE1FF
        2424BCFF0808B4FF0808B9FF0808BAFF0808BBFF0F0EABFFA1A2D5FEC0C0C0FC
        6A6A6B946A6A6BC0D9D8D7FE9999D1FF3838BBFF3636BCFF2C2CB7FF0909ADFF
        0A0AA9FF0A0AA4FF1C1CAFFFB1B1CFFFD6D6CBFFCCCCCBFFC7C7C7FFE4E4E1FF
        ECECEEFFACACB7FFC2C2BCFFBEBEBFFFC0C0C0FFCFCFC6FFC1C1D5FF2727B8FF
        0909ACFF0909B2FF0909B2FF0909B4FF0808B4FF0E0EB5FF6E6EBFFFD9D9D9FE
        6A6A6BC06A6A6BE1EBEAEBFF7D7CC7FF3838BFFF3434BEFF3536BEFF2A2AB8FF
        0909B0FF0909ACFF0A0AA8FF1C1CB1FFB2B2D0FFD7D7CCFFCBCBCBFFC7C7C8FF
        C8C8C3FFC6C6C3FFBFBFC1FFBDBDBDFFC5C5BCFFB8B8CEFF2929B5FF0A0AA8FF
        0909ACFF0909ADFF0909AFFF0909AFFF0909AFFF0C0CB0FF4747AFFFECECEDFF
        6A6A6BE16A6A6BF8F9F9F9FF6666C1FF3838C4FF3535C2FF3434C0FF3535BEFF
        3030BCFF1313B4FF0909ADFF0A0AA8FF1E1EB3FFAAAAD0FFD3D3CDFFCCCCCCFF
        C8C8C8FFC3C3C3FFC2C2C1FFC4C4BFFFB2B2CBFF2B2BB4FF0A0AA4FF0A0AA8FF
        0A0AA8FF0A0AA9FF0A0AA9FF0A0AA9FF0A0AA9FF0B0BA9FF3131A6FFFAFAFAFF
        6A6A6BF86A6A6BF8FBFBFBFF5959BEFF3B3BCAFF3A3AC8FF3737C4FF3535C2FF
        3636C0FF3636BEFF2323B8FF0909B1FF0A0AA7FF4949BEFFD6D6D4FFD3D3D1FF
        CDCDCDFFC8C8C8FFC4C4C3FFEDEDEDFF5F5FB3FF0C0C98FF0A0AA7FF0A0AA6FF
        0A0AA6FF0A0AA6FF0A0AA4FF0A0AA6FF0A0AA4FF0B0BA4FF2D2DA6FFFBFBFBFF
        6A6A6BF86A6A6BE1EDEDEEFF7F80CBFF4041CCFF3C3CCAFF3A3AC8FF383AC8FF
        3838C4FF3636C2FF3939C0FF2123B7FF4A4AC2FFCBCBDEFFE0E0DCFFD6D6D6FF
        D2D2D3FFCDCDCEFFC9C9C9FFE2E2E1FFF1F1F2FF4242A3FF0C0C99FF0A0AA4FF
        0A0AA4FF0A0AA4FF0B0BA3FF0B0BA3FF0B0BA1FF0E0EA1FF4443B0FFEDEDEEFF
        6A6A6BE16A6A6BC0DADADAFF9C9BD5FE4949CDFF3E3DD0FF3C3DCEFF3C3CCAFF
        3A3AC8FF3B39C7FF2828BDFF5C5CCCFFE5E5EDFFF4F4EDFFE5E5E6FFDEDEDEFF
        DCDCD9FFD9D9D3FFCDCDCDFFC8C8C8FFE5E5E1FFF1F1F3FF3F3FA0FF0C0C99FF
        0A0AA4FF0B0BA1FF0B0BA0FF0B0BA0FF0B0B9FFF1313A2FF6B6BC0FFDADADAFF
        6A6A6BC06A6A6B94C0C0C0FDBDBAE1FE5655CFFF4141D4FF3F3FD2FF3F3FCEFF
        3D3DCCFF2C2AC3FF5E5ED3FFEBEBF6FFFFFFFAFFF1F1F1FFEDEDEEFFF0F0E9FF
        D2D2E6FFBDBDD6FFDADAD3FFCFCFCFFFC9C9CAFFE5E5E2FFF1F1F3FF3A3AA0FF
        0C0C98FF0B0BA3FF0B0B9FFF0B0B9EFF0B0B9EFF1C1CA4FF9C9CD3FFC1C1C1FD
        6A6A6B946A6A6B609F9F9FF6DAD9EAFF6B6BCFFF4444D7FF4143D6FF4242D3FF
        3434CDFF6464DBFFEFEFFFFFFFFFFFFFFCFCFCFFF6F6F6FFFCFCF4FFE2E1F0FF
        5050CCFF4040C1FFC3C3DBFFE1E1D8FFD4D4D5FFCFCFCFFFE8E8E5FFF2F2F4FF
        4040A2FF0C0C99FF0F0FA2FF0F0FA0FF0F0F9DFF302FA9FFD1D1E8FEA0A0A0F6
        6A6A6B606A6A6B25787879F1E9E9EBFEA7A7DAFF6060DBFF4547DBFF3C3CD6FF
        5857DEFFF2F2FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE8E8F8FF5B5BD4FF
        2828BDFF2A2BBDFF4949C5FFC3C3DBFFE4E4DAFFD5D5D5FFCECED0FFE8E8E5FF
        F4F4F4FF4949AFFF2121A6FF2A2AA6FF2C2BA9FF5557B8FFEAEAECFE787879F1
        6A6A6B25000000007575769EBEBEBEFDC9CAE6FF7A79DBFF4C4CDFFF4141DBFF
        5757E0FFEAEAFFFFFFFFFFFFFFFFFFFFFFFFFFFFE8E7FFFF5B5BD7FF2E2EC6FF
        3E3EC9FF3A3AC5FF2C2EC1FF4A49C8FFC2C2DDFFE3E3DAFFD5D5D4FFDADAD3FF
        CACBD9FF4747BBFF2525ADFF2C2BACFF3332AEFFA5A4D8FFBFBFBFFD7575769E
        00000000000000006A6A6B40888889F0ECECEFFE9696D6FF7B7BE3FF4D4BE0FF
        4141DBFF5F5FE6FFE7E7FFFFFFFFFFFFE9E9FFFF5A5ADCFF3333CAFF4242CFFF
        4040CBFF3D3DC9FF3D3EC8FF3030C2FF4848C9FFC0C0DDFFECEEDEFFD0D0E0FF
        5554C7FF2828B3FF3232B4FF3434B1FF5453B7FFECECEFFE888889F06A6A6B40
        0000000000000000000000007C7C7C98B7B7B8FAD0D0ECFF8F8FDBFF6868E3FF
        4E4EE2FF3E40DBFF6565E9FFB2B2F7FF6565E4FF393BD2FF4646D7FF4343D4FF
        4343D1FF4242CFFF4040CBFF3F3FCAFF3333C4FF4E4ECBFF9E9EE2FF5C5BCFFF
        292ABAFF3636BCFF3938B8FF3F3EB1FFCBCBE9FFB7B7B8FA7C7C7C9800000000
        0000000000000000000000006A6A6B22838383D8D3D3D3FEB5B5E2FF9E9EE4FF
        6766E2FF4E50E6FF4646E0FF3D3DDAFF4444DCFF4B4BDCFF4848DBFF4847D9FF
        4646D5FF4443D3FF4343D1FF4242CFFF4143CDFF3A3AC8FF312FC5FF3535C3FF
        3C3CC3FF3D3DBEFF403FB5FFACACDCFFD3D3D3FE838383D86A6A6B2200000000
        000000000000000000000000000000006A6A6B43878788EAE1E1E1FFB5B5E2FF
        A7A6E4FF7877E5FF5151E5FF4F4FE4FF4E4EE2FF4D4DE0FF4C4CDEFF4B4BDCFF
        4949DBFF4848D7FF4747D5FF4545D3FF4545D1FF4343CFFF4242CCFF3F3FCBFF
        4343C2FF4645B6FFADADDCFFE1E1E1FF878788EA6A6A6B430000000000000000
        00000000000000000000000000000000000000006A6A6B4E878788EAD3D3D3FE
        D0D0ECFFAAA9DFFFA2A2ECFF6565E3FF5151E6FF4F4FE4FF4F4DE4FF4D4DE0FF
        4D4DDFFF4D4DDCFF4C49DBFF4A4AD8FF4749D6FF4747D4FF4949CBFF4B4BC3FF
        8E8ED0FFCDCCE8FFD3D3D3FE878788EA6A6A6B4E000000000000000000000000
        0000000000000000000000000000000000000000000000006A6A6B43838383D8
        B7B7B8FAECECEFFEC3C2E5FFADAEE1FF9E9DE8FF6F6FE0FF5C5CE1FF5452E2FF
        5051E1FF4F4FDFFF4F4FDBFF5150D6FF5151CFFF5F5FC8FFA1A1D3FEC7C8E0FE
        E4E4E7FEB7B7B8FA838383D86A6A6B4300000000000000000000000000000000
        000000000000000000000000000000000000000000000000000000006A6A6B22
        7C7C7C98888889F0BFBFBFFDEBEBECFED8D9EBFEBDBDE4FEA8A7DCFF9695D7FF
        8886D4FF7F7DCEFF8C8BD2FFA1A2D9FFC0BEE1FED9D9EAFEEAEAECFEBFBFBFFD
        888889F07C7C7C986A6A6B220000000000000000000000000000000000000000
        0000000000000000000000000000000000000000000000000000000000000000
        000000006A6A6B407575769E787879F19F9F9FF6C0C0C0FDDADADAFFEDEDEEFF
        FBFBFBFFFBFBFBFFEDEDEEFFDADADAFFC0C0C0FD9F9F9FF6787879F17575769E
        6A6A6B4000000000000000000000000000000000000000000000000000000000
        0000000000000000000000000000000000000000000000000000000000000000
        0000000000000000000000006A6A6B256A6A6B606A6A6B946A6A6BC06A6A6BE1
        6A6A6BF86A6A6BF86A6A6BE16A6A6BC06A6A6B946A6A6B606A6A6B2500000000
        0000000000000000000000000000000000000000000000000000000000000000
        00000000FFC003FFFF0000FFFC00003FF800001FF000000FE0000007C0000003
        C000000380000001800000010000000000000000000000000000000000000000
        0000000000000000000000000000000000000000000000000000000080000001
        80000001C0000003C0000003E0000007F000000FF800001FFC00003FFF0000FF
        FFC003FF}
      ShowUnicode = False
      ColWidths = (
        30)
      RowHeights = (
        17
        17)
    end
    object PanelUbicacionActiva: TPanel
      Left = 0
      Top = 22
      Width = 298
      Height = 59
      Align = alTop
      Color = clBlack
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWhite
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentBackground = False
      ParentFont = False
      TabOrder = 2
      Visible = False
      object Label1: TLabel
        Left = 1
        Top = -2
        Width = 94
        Height = 19
        Caption = 'Ubic.ACTIVA'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
      object lbUbicacionActiva: TLabel
        Left = 11
        Top = 23
        Width = 8
        Height = 33
        Caption = '-'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = -24
        Font.Name = 'Arial Black'
        Font.Style = []
        ParentFont = False
        OnClick = lbUbicacionActivaClick
      end
      object lblObsUbcacionActiva: TLabel
        Left = 137
        Top = 22
        Width = 8
        Height = 33
        Caption = '-'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = -24
        Font.Name = 'Arial Black'
        Font.Style = []
        ParentFont = False
        OnClick = lbUbicacionActivaClick
      end
      object Label3: TLabel
        Left = 133
        Top = -2
        Width = 42
        Height = 19
        Caption = 'Libres'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
      end
    end
  end
  object PanelFiltroDetalle: TPanel
    Left = 0
    Top = 61
    Width = 298
    Height = 30
    Align = alTop
    Color = clBackground
    ParentBackground = False
    TabOrder = 3
    Visible = False
    object ComboFiltroDetalle: TComboBox
      Left = 1
      Top = 1
      Width = 120
      Height = 41
      Align = alLeft
      Style = csDropDownList
      Color = clBlack
      DropDownCount = 6
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWhite
      Font.Height = -27
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      OnChange = ComboFiltroDetalleChange
      Items.Strings = (
        ''
        'Entradas'
        'Internas')
    end
  end
  object dtsListadoOrdenesUbicacion: TUniDataSource
    Left = 176
    Top = 224
  end
  object GestureManager1: TGestureManager
    Left = 184
    Top = 176
    GestureData = <
      item
        Control = panelDetalle
        Collection = <
          item
            GestureID = sgiUp
          end
          item
            GestureID = sgiDown
          end
          item
            GestureID = sgiCheck
          end
          item
            GestureID = sgiUpLeftLong
          end
          item
            GestureID = sgiDownLeftLong
          end>
      end
      item
        Control = dbgListadoOrdenesUbicacion
        Collection = <
          item
            GestureID = sgiUp
          end
          item
            GestureID = sgiDown
          end
          item
            GestureID = sgiLeft
          end>
      end>
  end
end
