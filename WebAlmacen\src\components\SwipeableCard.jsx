import React, { useRef, useState } from 'react';

/**
 * Componente reutilizable para tarjetas con gestos de swipe (izquierda/derecha) tipo móvil.
 * Props:
 *  - children: contenido de la tarjeta
 *  - onSwipeLeft: callback para swipe a la izquierda
 *  - onSwipeRight: callback para swipe a la derecha
 *  - leftOptions/rightOptions: JSX para mostrar al hacer swipe
 */
export default function SwipeableCard({ children, onSwipeLeft, onSwipeRight, leftOptions, rightOptions }) {
  const touchStartX = useRef(null);
  const [swipe, setSwipe] = useState(null); // 'left', 'right', o null
  const [active, setActive] = useState(false);

  function handleTouchStart(e) {
    touchStartX.current = e.touches[0].clientX;
    setSwipe(null);
    setActive(true);
  }

  function handleTouchMove(e) {
    if (touchStartX.current === null) return;
    const dx = e.touches[0].clientX - touchStartX.current;
    if (dx > 60) {
      setSwipe('right');
    } else if (dx < -60) {
      setSwipe('left');
    } else {
      setSwipe(null);
    }
  }

  function handleTouchEnd() {
    if (swipe === 'left' && onSwipeLeft) onSwipeLeft();
    if (swipe === 'right' && onSwipeRight) onSwipeRight();
    setTimeout(() => setSwipe(null), 300);
    setActive(false);
    touchStartX.current = null;
  }

  // Para desktop (mouse)
  function handleMouseDown() {
    setActive(true);
  }
  function handleMouseUp() {
    setActive(false);
  }
  function handleMouseLeave() {
    setActive(false);
  }

  return (
    <div
      className={`swipeable-card${swipe ? ' swipe-' + swipe : ''}${active ? ' active' : ''}`}
      style={{ position: 'relative', overflow: 'hidden', touchAction: 'pan-y' }}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
    >
      {/* Opciones a la izquierda */}
      {swipe === 'right' && (
        <div className="swipe-options swipe-options-right">{rightOptions}</div>
      )}
      {/* Opciones a la derecha */}
      {swipe === 'left' && (
        <div className="swipe-options swipe-options-left">{leftOptions}</div>
      )}
      {/* Contenido principal de la tarjeta */}
      {typeof children === 'function'
        ? children({ active })
        : React.isValidElement(children)
          ? React.cloneElement(children, { active })
          : <div className="swipeable-card-content" style={active ? {background:'#e0f7fa',border:'3px solid #00bcd4',transition:'background 0.2s, border 0.2s'} : {transition:'background 0.2s, border 0.2s'}}>{children}</div>
      }
    </div>
  );
}
