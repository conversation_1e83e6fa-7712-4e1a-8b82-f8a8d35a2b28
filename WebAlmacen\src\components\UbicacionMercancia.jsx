import React, { useState, useRef, useEffect } from 'react';
import { fakeEntradas } from '../data/fakeEntradas.js';
import ToggleView from './ToggleView.jsx';
import SwipeableCard from './SwipeableCard.jsx';

export default function UbicacionMercancia({ onBack, onShowDetalle }) {
  const [view, setView] = useState('cards');
  const [input, setInput] = useState("");
  const inputRef = useRef(null);

  useEffect(() => {
    setTimeout(() => {
      inputRef.current && inputRef.current.focus();
    }, 100);
  }, []);

  return (
    <div className="ubicacion-wrapper">
      <div className="ubicacion-header">
        <button className="ubicacion-btn-back" onClick={onBack}>Volver</button>
        <h3>Ubicación Mercancía</h3>
        <div style={{flex: 1}}></div>
      </div>
      <div className="ubicacion-input-box">
        <input
          id="ubicacion-mercancia-input"
          ref={inputRef}
          type="text"
          placeholder="Instrucción o ubicación..."
          value={input}
          onChange={e => setInput(e.target.value)}
          autoFocus
        />
      </div>
      <ToggleView view={view} setView={setView} />
      {view === 'cards' ? (
        <div className="ubicacion-card-list">
          {fakeEntradas.map((item, idx) => (
            <SwipeableCard
              key={idx}
              onSwipeLeft={() => alert('Acción ejemplo: Eliminar')}
              onSwipeRight={() => alert('Acción ejemplo: Editar')}
              leftOptions={<button style={{color:'white',background:'red',padding:8,border:'none',borderRadius:4}}>Eliminar</button>}
              rightOptions={<button style={{color:'white',background:'green',padding:8,border:'none',borderRadius:4}}>Editar</button>}
            >
              {({ active }) => (
                <div
                  className="ubicacion-card ubicacion-card-pro"
                  onClick={() => onShowDetalle(item)}
                  style={{
                    cursor: 'pointer',
                    background: active ? '#e0f7fa' : undefined,
                    border: active ? '3px solid #00bcd4' : undefined,
                    transition: 'background 0.2s, border 0.2s'
                  }}
                >
                  <div className="ubicacion-card-row ubicacion-card-row-header">
                    <span className="doc">{item.doc || <span className="empty-cell">-</span>}</span>
                    <span className="sscc">{item.sscc}</span>
                    <span className="matexterna">{item.matExterna}</span>
                  </div>
                  <div className="ubicacion-card-row ubicacion-card-row-data">
                    <span className="uds">Uds: <b>{item.uds}</b></span>
                    <span className="lin">Lin: <b>{item.lin}</b></span>
                  </div>
                </div>
              )}
            </SwipeableCard>
          ))}
        </div>
      ) : (
        <div className="ubicacion-table-wrapper">
          <table className="ubicacion-table ubicacion-table-pro">
            <thead>
              <tr>
                <th>Doc</th>
                <th>SSCC</th>
                <th>Mat.Externa</th>
                <th>Uds</th>
                <th>Lin.</th>
              </tr>
            </thead>
            <tbody>
              {fakeEntradas.map((item, idx) => (
                <tr key={idx} style={{ cursor: 'pointer' }} onClick={() => onShowDetalle(item)}>
                  <td>{item.doc || <span className="empty-cell">-</span>}</td>
                  <td>{item.sscc}</td>
                  <td>{item.matExterna}</td>
                  <td>{item.uds}</td>
                  <td>{item.lin}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
