// Herramienta Node.js para convertir la sección FP1_SCROLLBOX_ARTICULO de un .ini a JSON
const fs = require('fs');
const path = require('path');

const iniPath = path.join(__dirname, '../OriginalDelphi/MALASA.ini');
const jsonPath = path.join(__dirname, '../fp_configuracion.json');

function parseIniAllSections(content) {
  const sectionRegex = /\[([^\]]+)\]([\s\S]*?)(?=\n\[|$)/g;
  let match;
  const result = {};
  while ((match = sectionRegex.exec(content)) !== null) {
    const section = match[1].trim();
    const lines = match[2].split(/\r?\n/).filter(line => line.trim() && !line.trim().startsWith(';'));
    const obj = {};
    for (const line of lines) {
      const [key, ...rest] = line.split('=');
      if (!key || rest.length === 0) continue;
      let value = rest.join('=').trim();
      // Convertir números si aplica
      if (/^-?\d+$/.test(value)) value = Number(value);
      obj[key.trim()] = value;
    }
    result[section] = obj;
  }
  return result;
}

function main() {
  if (!fs.existsSync(iniPath)) {
    console.error('No se encontró el archivo INI');
    process.exit(1);
  }
  const content = fs.readFileSync(iniPath, 'utf8');
  const config = parseIniAllSections(content);
  if (!config || Object.keys(config).length === 0) {
    console.error('No se encontraron secciones en el INI');
    process.exit(2);
  }
  fs.writeFileSync(jsonPath, JSON.stringify(config, null, 2), 'utf8');
  console.log('Configuración exportada a', jsonPath);
}

if (require.main === module) {
  main();
}
