unit uFormArticuloFP02;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, uFPPanel, ExtCtrls, intFormAux2FormPpal,
  uFrmConsultaOrdenUbicFP02,JVLED,AdvShapeButton;

type
  TEstadosArticulos = (ESTADO_PTESERVIR, ESTADO_SERVIDO, ESTADO_SERVICIOPARCIAL,
    ESTADO_ANULADO, ESTADO_SERVIDOEXCESO);

  TScrollBoxArticulos = class;


  TPanelArticuloFP02 = class(TFPPanel, IFPMapaUbicacionesRetorno)
  private
    LbLote: TLabel;

    lbUDS:TLabel;
    lbEnvase:TLabel;


    FSeleccionado: boolean;
    FPermitirSeleccionado: boolean;
    // FUbicacion: string;
    FIdUbicacion: integer;
    FIdArticulo: integer;
    FCB: string;
    FIntFP02: IFP02;
    FIntFPScrollBox: IFPScrollBox;
    btnMapa: TButton;

    ListaCB: TStringList;
    ListaSSCC: TStringList;

    procedure FormClick(Sender: TObject);
    procedure setSeleccionado(value: boolean);
    procedure setPermitirSeleccionado(value: boolean);
    procedure CambiarColorFuente;
    procedure CambiarColorFondo;
    function ObtenerColorFuente: TColor;
    procedure crearEtiquetas;
    procedure setCodigoBarras(const value: string);
    procedure mostrarCambioEstado;
    procedure CambiarPantalla(value: boolean);
    procedure btnMapaClick(Sender: TObject);
    procedure inicializarSSCC;
    procedure inicializarCB;

    procedure setUbicado(const value: string);
    procedure actualizarUbicacion(idUbicacion: integer; Anagrama: string);
    procedure JV1Click(Sender: TObject);

  public
    lbCodigo: TLabel;
    lbDescripcion1: TLabel;
    lbDescripcion2: TLabel;
    lbDescripcion3: TLabel;
    lbDescripcion4: TLabel;
    lbStatusSSCC           : TLabel;
    lbReferenciaProveedor: TLabel;
    lbCodigoBarras: TLabel;
    LbUbicacionActual: TLabel;
    lbObservaciones: TLabel;
    LbSSCC: TLabel;
    lbUbicacionPropuesta: TLabel;
    FLOTE: string;
    FUbicado: string;
    FCantidad: string;
    Fenvases: string;
    FIdOrdenUbicacion: integer;

    FFecharealizacionDet :TdateTime;
    JvLED1 : TJvLED;
    ADVLED1: TAdvShapeButton;
     procedure BtnMensajeEmergente(Sender: TObject);
     constructor create(AOwner: TComponent; AIntFP02: IFP02;
      AIntFPScrollBox: IFPScrollBox; AIdOrdenUbicacion: integer;
      ACodigo, ADescripcion1, ADescripcion2, AAnagramaIni, AAnagramaFin,
      AUbicado, ALote, ACantidad,ASSCC ,AEnvase ,ACodigoBarras,AreferenciaProveedor ,Aobservaciones,
      ADescripcion3,ADescripcion4  ,AStatusSSCC
      : string; AIdUbicacionFin, AIdArticulo: integer;AfechaRealizacionDet:TdateTime); overload;
    destructor destroy; override;
    procedure PantallaCambiarEstado;

    property seleccionado: boolean read FSeleccionado write setSeleccionado;
    property PermitirSeleccionado: boolean write setPermitirSeleccionado;
    // property Ubicacion: string read FUbicacion;
    property idUbicacion: integer read FIdUbicacion;
    property IdArticulo: integer read FIdArticulo;
    function ComprobarCBArticulo(ACB: string): boolean;
    function ComprobarSSCC(ACB: string): boolean;

    property CodigoBarras: string write setCodigoBarras;
    property idOrdenUbicacionDet: integer read FIdOrdenUbicacion;
    property Ubicado: string write setUbicado;

  end;

  TScrollBoxArticulos = class(TScrollBox, IFPScrollBox)
  private
    FPanelArticulo: TPanelArticuloFP02;
  public
    constructor create(AOwner: TComponent); overload;
    destructor destroy; override;
    procedure PanelSeleccionado(APanelArticulo: TFPPanel);

    property PanelArticulo: TPanelArticuloFP02 read FPanelArticulo;
  end;

implementation


uses uVariablesPrograma,uPanelMapaFP;

{ TFormArticulo }

procedure TPanelArticuloFP02.mostrarCambioEstado;
begin
  CambiarPantalla(false);
end;

procedure TPanelArticuloFP02.actualizarUbicacion(idUbicacion: integer;
  Anagrama: string);
begin
  FIdUbicacion := idUbicacion;
  lbUbicacionPropuesta.Caption := Tvariablesprograma.GetInstance.TxUbicacionPropuesta_FP2+' ' + Anagrama;
end;

procedure TPanelArticuloFP02.btnMapaClick(Sender: TObject);
begin
  FIntFP02.FocoEditPpal;
  FIntFP02.mostrarMapa(Self, FIdArticulo, FIdUbicacion, FIdOrdenUbicacion);
end;

procedure TPanelArticuloFP02.CambiarColorFondo;
begin
  if FSeleccionado then
    Color := $00F5CEF6 // clFuchsia
  else
  begin
    if (FUbicado = 'S') and (FfechaRealizacionDet >= StrtoDAte('01/01/2003')) then
      Color := clBlack
    else if (FUbicado = 'S') and (FfechaRealizacionDet < StrtoDAte('01/01/2003')) then
      Color := clsilver
    else
      Color := clWhite;
  end;
end;

function TPanelArticuloFP02.ObtenerColorFuente: TColor;
begin
  if FSeleccionado then
    Result := clBlack
  else
  begin
    if FUbicado = 'N' then
      Result := clBlack
    else
      Result := clWhite;
  end;


end;

procedure TPanelArticuloFP02.PantallaCambiarEstado;
begin
end;

procedure TPanelArticuloFP02.setCodigoBarras(const value: string);
var
  i: integer;
begin
  if not ListaCB.Find(value, i) then
    ListaCB.add(value);
  FCB := value;
end;

procedure TPanelArticuloFP02.setPermitirSeleccionado(value: boolean);
begin
  FPermitirSeleccionado := value;
end;

procedure TPanelArticuloFP02.setSeleccionado(value: boolean);
begin
  FSeleccionado := value;
  CambiarColorFuente;
  CambiarColorFondo;
end;

procedure TPanelArticuloFP02.setUbicado(const value: string);
begin
  FUbicado := value;
  if (FUbicado='S') then FSeleccionado:=false;
  btnMapa.Enabled := (FUbicado = 'N');
  CambiarColorFuente;
  CambiarColorFondo;
end;

procedure TPanelArticuloFP02.CambiarColorFuente;
var
  Color: TColor;
begin
  Color := ObtenerColorFuente;
  lbCodigo.Font.Color := Color;
  lbCodigoBarras.Font.Color := Color;
  lbReferenciaProveedor.Font.Color := Color;
  LbUbicacionActual.Font.Color := Color;



//  lbDescripcion1.Font.Color := Color;
 // lbDescripcion2.Font.Color := Color;


   if Tvariablesprograma.GetInstance.lbdescripcion1_color_FP2 <> '' then
   lbDescripcion1.Font.Color := StringToColor(Tvariablesprograma.GetInstance.lbdescripcion1_color_FP2)
  else
   lbDescripcion1.Font.Color := Color;


     if Tvariablesprograma.GetInstance.lbdescripcion2_color_FP2 <> '' then
   lbDescripcion2.Font.Color := StringToColor(Tvariablesprograma.GetInstance.lbdescripcion2_color_FP2)
  else
   lbDescripcion2.Font.Color := Color;

  if Tvariablesprograma.GetInstance.lbdescripcion3_color_FP2 <> '' then
   lbDescripcion3.Font.Color := StringToColor(Tvariablesprograma.GetInstance.lbdescripcion3_color_FP2)
  else
   lbDescripcion3.Font.Color := Color;


  if Tvariablesprograma.GetInstance.lbdescripcion4_color_FP2 <> '' then
   lbDescripcion4.Font.Color := StringToColor(Tvariablesprograma.GetInstance.lbdescripcion4_color_FP2)
  else
   lbDescripcion4.Font.Color := Color;

   lbObservaciones.Font.Color := Color;
  lbUbicacionPropuesta.Font.Color := Color;
  lbUDS.Font.Color := Color;
  LbLote.Font.Color := Color;
  LbSSCC.Font.Color := Color;
  LbEnvase.Font.Color := Color;


    if Tvariablesprograma.GetInstance.Estandar_MARISMAS='S' then
  begin
    if lbdescripcion2.caption='2.-LEIDO' then
       lbdescripcion2.Font.Color:=clGreen;
    if uppercase(lbdescripcion2.caption)='-1.-RECHAZO' then
       lbdescripcion2.Font.Color :=clRED;
   // if uppercase(lbdescripcion2.caption)='0.-GENERADO' then
     //  Result :=clRED;

    if uppercase(lbdescripcion2.caption)='1.-IMPRESO' then
       lbdescripcion2.Font.Color :=clBlue;

    if uppercase(lbdescripcion2.caption)='-9.-ANULADO' then
       lbdescripcion2.Font.Color :=clGray;



  end;
    if Tvariablesprograma.GetInstance.NOMBRE_ALMACEN='LASERGALICIA' then
  begin

    if uppercase(lbStatusSSCC.caption)<>'' then
       lbStatusSSCC.Font.Color :=clBlue;
  end;


end;

function TPanelArticuloFP02.ComprobarCBArticulo(ACB: string): boolean;
var
  i: integer;
begin
  Result := (ListaCB.Find(ACB, i));
end;

function TPanelArticuloFP02.ComprobarSSCC(ACB: string): boolean;
var
  i: integer;
begin
  Result := (ListaSSCC.Find(ACB, i));
end;

procedure TPanelArticuloFP02.CambiarPantalla(value: boolean);
begin
  lbCodigo.Visible := value;
  lbCodigoBarras.Visible := value;
  lbReferenciaProveedor.Visible := value;
  lbObservaciones.Visible := value;


  LbUbicacionActual.Visible := value;
  lbDescripcion1.Visible := value;
  lbDescripcion2.Visible := value;
  lbDescripcion3.Visible := value;
  lbDescripcion4.Visible := value;
  lbStatusSSCC.Visible := value;


  lbUbicacionPropuesta.Visible := value;
  lbUDS.Visible := value;
  LbLote.Visible := value;
  LbSSCC.Visible := value;
  LbEnvase.Visible := value;

end;

procedure TPanelArticuloFP02.crearEtiquetas;
var
  TopLinea1, TopLinea2, TopLinea3, TopLinea4, TopLinea5, TopLinea6: integer;
begin
  with Tvariablesprograma.GetInstance do
 begin
  TopLinea1 := TopLinea1_FP2;//1;
  TopLinea2 := TopLinea2_FP2;//15;
  TopLinea3 := TopLinea3_FP2;//29;
  TopLinea4 := TopLinea4_FP2;//43;
  TopLinea5 := TopLinea5_FP2;//57;
  //  TopLinea6 := 70;

  lbUDS := TLabel.create(Self);
  CrearEtiqueta(lbUDS, lbUds_left_fp2, lbUds_linea_fp2, 190, 13, TxUDS_FP2, taLeftJustify);
  lbUDS.Font.Size:= lbUDS_font_FP2;
  lbUDS.OnClick := FormClick;


  lbCodigo := TLabel.create(Self);
  CrearEtiqueta(lbCodigo, lbCodigo_left_fp2, lbCodigo_linea_fp2, 30, 13, TxCodigo_FP2, taLeftJustify);
  lbCodigo.Font.Size:= lbCodigo_font_FP2;
  lbCodigo.OnClick := FormClick;

  lbCodigoBarras := TLabel.create(Self);
  CrearEtiqueta(lbCodigoBarras, lbCodigoBarras_left_fp2, lbCodigoBarras_linea_fp2, 30, 13, TxCodigoBarras_FP2, taLeftJustify);
  lbCodigoBarras.Font.Size:= lbCodigoBarras_font_FP2;
  lbCodigoBarras.OnClick := FormClick;

  lbReferenciaProveedor := TLabel.create(Self);
  CrearEtiqueta(lbReferenciaProveedor, lbReferenciaProveedor_left_fp2, lbReferenciaProveedor_linea_fp2, 30, 13, TxReferenciaProveedor_FP2, taLeftJustify);
  lbReferenciaProveedor.Font.Size:= lbReferenciaProveedor_font_FP2;
  lbReferenciaProveedor.OnClick := FormClick;

  lbDescripcion1 := TLabel.Create(Self);
  CrearEtiqueta(lbDescripcion1, lbDescripcion1_left_fp2,lbDescripcion1_linea_fp2, 135, 13, '', taLeftJustify);
  lbDescripcion1.OnClick := FormClick;

  lbDescripcion2 := TLabel.Create(Self);
  CrearEtiqueta(lbDescripcion2, lbDescripcion2_left_fp2,lbDescripcion2_linea_fp2, 135, 13, '', taLeftJustify);
  lbDescripcion2.Font.Size:= lbDescripcion2_font_FP2;
  lbDescripcion2.OnClick := FormClick;

  lbDescripcion3 := TLabel.Create(Self);
  CrearEtiqueta(lbDescripcion3, lbDescripcion3_left_fp2,lbDescripcion3_linea_fp2, 135, 13, '', taLeftJustify);
  lbDescripcion3.Font.Size:= lbDescripcion3_font_FP2;
  lbDescripcion3.OnClick := FormClick;

  lbDescripcion4 := TLabel.Create(Self);
  CrearEtiqueta(lbDescripcion4, lbDescripcion4_left_fp2,lbDescripcion4_linea_fp2, 135, 13, '', taLeftJustify);
  lbDescripcion4.Font.Size:= lbDescripcion4_font_FP2;
  lbDescripcion4.OnClick := FormClick;


   lbStatusSSCC := TLabel.Create(Self);
  CrearEtiqueta(lbStatusSSCC, lbStatusSSCC_left_fp2,lbStatusSSCC_linea_fp2, 135, 13, '', taLeftJustify);
  lbStatusSSCC.Font.Size:= lbStatusSSCC_font_FP2;
  lbStatusSSCC.OnClick := FormClick;


  LbUbicacionActual := TLabel.Create(Self);
  CrearEtiqueta(LbUbicacionActual, lbUbicacionActual_left_fp2,lbUbicacionActual_linea_fp2, 29, 13, TxUbicacionActual_FP2, taLeftJustify);
  LbUbicacionActual.Font.Size:= LbUbicacionActual_font_FP2;
  LbUbicacionActual.OnClick := FormClick;

  lbUbicacionPropuesta := TLabel.Create(Self);
  CrearEtiqueta(lbUbicacionPropuesta, lbUbicacionPropuesta_left_fp2,lbUbicacionPropuesta_linea_fp2, 115, 13,TxUbicacionPropuesta_FP2, taLeftJustify);
  lbUbicacionPropuesta.Font.Size:= lbUbicacionPropuesta_font_FP2;
  lbUbicacionPropuesta.OnClick := FormClick;

  lbLote := TLabel.Create(Self);
  lbLote.AutoSize := false;
  CrearEtiqueta(lbLote, lbLote_left_fp2,lbLote_linea_fp2, 150, 14, TxLote_FP2, taLeftJustify);
  lbLote.Font.Size:= lblote_font_FP2;

  lbSSCC:= TLabel.Create(Self);
  CrearEtiqueta(lbSSCC, lbsscc_left_fp2,lbsscc_linea_fp2, 190, 13,'Mat:', taLeftJustify);
  lbSSCC.Font.Size:= lbSSCC_font_FP2;
  lbSSCC.OnClick := FormClick;

  lbenvase := TLabel.Create(Self);
  CrearEtiqueta(lbEnvase, lbenvase_left_fp2,lbenvase_linea_fp2, 100, 13, TxEnvase_FP2, taLeftJustify);//PYB
  lbenvase.Font.Size:= lbenvase_font_FP2;
  lbenvase.OnClick := FormClick;

  lbObservaciones  := TLabel.create(Self);
  CrearEtiqueta(lbObservaciones, lbObservaciones_left_fp2, lbObservaciones_linea_fp2, 30, 13, TxObservaciones_FP2, taLeftJustify);
  lbObservaciones.Font.Size:= lbObservaciones_font_FP2;
  lbObservaciones.OnClick := FormClick;


   JvLED1 := TJvLED.Create(Self);
  with JvLED1 do
  begin
    Top := TopLinea1;
    Left :=TVariablesPrograma.GetInstance.JVLed1_Left_FP2;//130
    autosize:=true;
    Parent := Self;
    ColorOn:=clred;
   // OnClick := JV1Click;
     if tvariablesprograma.GetInstance.NOMBRE_ALMACEN='TERSA' then
      OnClick := JV1Click
     else
    OnClick:=BtnMensajeEmergente;
  // visible:=false; //visible:=true; COmentado porque solo debe de salir
    //por ejemplo en Alco cuando hay un error de ubicacion
    //No se si se pusoporque obligatoriamente debia de verse
  end;

  ADVLED1 := TAdvShapeButton.Create(Self);
  with ADVLED1 do
  begin
    Width := 15;
    Height := 14;
    Top := TopLinea1; // TopLinea3-2;
    Left := TVariablesPrograma.GetInstance.JVLed1_Left_FP2-3;

    picture.LoadFromFile('.\logos\ledrojo1000.png');
    //    picturedown.LoadFromFile('.\logos\boton_CkeckBokChecked1000.png');
          Appearance.Shape := bsRectangle;
          Appearance.BorderColor := clBlack;
          Appearance.BorderColorHot := clBlack;
          Appearance.BorderColorDown := clBlack;
          Appearance.BorderColorDisabled := clBlack;
          Appearance.InnerBorderColor := clBlack;
          Appearance.InnerBorderColorHot := clBlack;
          Appearance.InnerBorderColorDown := clBlack;
          Appearance.InnerBorderColorDisabled := clBlack;
          Appearance.Color := clRed;
          Appearance.ColorTo := clRed;
          Appearance.ColorMirror := clRed;
          Appearance.ColorMirrorTo := clRed;
          Appearance.ColorHot := 16744448;
          Appearance.ColorHotTo := 16744448;
          Appearance.ColorHotMirror := 16744448;
          Appearance.ColorHotMirrorTo := 16744448;
          Appearance.ColorDown := 16744448;
          Appearance.ColorDownTo := 16744448;
          Appearance.ColorDownMirror := 16744448;
          Appearance.ColorDownMirrorTo := 16744448;
          Appearance.ColorDisabled := clRed;
          Appearance.ColorDisabledTo := clRed;
          Appearance.ColorDisabledMirror := clRed;
          Appearance.ColorDisabledMirrorTo := clRed;
          Appearance.MenuShapeColor := clRed;
          Appearance.MenuShapeColorHot := clRed;
          Appearance.MenuShapeColorDown := clRed;
          Appearance.MenuShapeColorDisabled := clRed;

   if TVariablesPrograma.GetInstance.JVled1_ADV = 'S' then
   begin

        visible:=true;
   end
   else
   begin
           visible:=false;
   end;


    Parent := Self;
    if tvariablesprograma.GetInstance.NOMBRE_ALMACEN='TERSA' then
      OnClick := JV1Click
     else
    OnClick := BtnMensajeEmergente;
  end;

 end;

end;

constructor TPanelArticuloFP02.create(AOwner: TComponent; AIntFP02: IFP02;
  AIntFPScrollBox: IFPScrollBox; AIdOrdenUbicacion: integer;
  ACodigo, ADescripcion1, ADescripcion2, AAnagramaIni, AAnagramaFin,
  AUbicado, ALote, ACantidad,ASSCC, AEnvase,ACodigoBarras,AreferenciaProveedor,Aobservaciones,
   ADescripcion3,ADescripcion4  ,AStatusSSCC
   : string; AIdUbicacionFin, AIdArticulo: integer;AfechaRealizacionDet:TdateTime);
begin
  inherited create(AOwner);
  FIntFP02 := AIntFP02;
  FIntFPScrollBox := AIntFPScrollBox;
  FIdOrdenUbicacion := AIdOrdenUbicacion;
  FUbicado := AUbicado;
  FfechaRealizacionDet:=AfechaRealizacionDet;

  Color := clWhite;
  Font.Color := clBlack;
  OnClick := FormClick;
  //Width := 225;
  //Height := 87;
  Width := strtoint(TVariablesPrograma.GetInstance.resolucion)-80; //strtoint(TVariablesPrograma.GetInstance.resolucion)-80;
  Height := TVariablesPrograma.GetInstance.HeightScrollBox_FP2;  //87

  crearEtiquetas;
   if TVariablesPrograma.GetInstance.HeightActual<strtoint(TVariablesPrograma.GetInstance.resolucion) then
    scaleby(strtoint(TVariablesPrograma.GetInstance.resolucion),320);

  Visible := false;
  FIdArticulo := AIdArticulo;
  lbCodigo.Caption := ACodigo;
  lbCodigoBarras.Caption := ACodigoBarras;
  lbReferenciaProveedor.Caption := AReferenciaProveedor;
  lbObservaciones.Caption:=Aobservaciones;
  lbDescripcion1.Caption := ADescripcion1;
  lbDescripcion2.Caption := ADescripcion2;
  lbDescripcion3.Caption := ADescripcion3;
  lbDescripcion4.Caption := ADescripcion4;
  lbStatusSSCC.Caption := AStatusSSCC;


  LbUbicacionActual.Caption := Tvariablesprograma.GetInstance.TxUbicacionActual_FP2+'' + AAnagramaIni;
  lbUbicacionPropuesta.Caption := Tvariablesprograma.GetInstance.TxUbicacionPropuesta_FP2+''+ AAnagramaFin;
  lbUDS.Caption:= Tvariablesprograma.GetInstance.TxUDS_FP2+'' + ACantidad;
  LbLote.Caption:= Tvariablesprograma.GetInstance.TxLote_FP2+ ' '+ ALote;
  LbSSCC.Caption:= ASSCC;
  LbEnvase.Caption := Tvariablesprograma.GetInstance.TxEnvase_FP2+ ' '+AEnvase;
  FcANTIDAD:=ACantidad ;
  Fenvases:= AEnvase;
  Flote:=Alote;

  FIdUbicacion := AIdUbicacionFin;
  PermitirSeleccionado := false;
  CambiarPantalla(True);

  btnMapa := TButton.create(Self);
  btnMapa.Parent := Self;
  btnMapa.Top := Tvariablesprograma.GetInstance.BtnMapa_linea_FP2;//43;
  btnMapa.Left :=Tvariablesprograma.GetInstance.BtnMapa_left_FP2;//155;
  btnMapa.Width := 60;
  btnMapa.Caption := 'Mapa';
  btnMapa.OnClick := btnMapaClick;
  btnMapa.Enabled := (FUbicado = 'N');
  FPermitirSeleccionado := True;
  CambiarColorFuente;
  CambiarColorFondo;
  inicializarCB;
  inicializarSSCC;
end;

destructor TPanelArticuloFP02.destroy;
begin
  freeandnil(lbCodigo);
  freeandnil(LbUbicacionActual);
  freeandnil(lbDescripcion1);
  freeandnil(lbDescripcion2);
  freeandnil(lbDescripcion3);
  freeandnil(lbDescripcion4);
   freeandnil(lbStatusSSCC );

  freeandnil(lbUbicacionPropuesta);
  freeandnil(lbUDS);
  freeandnil(LbLote);
  freeandnil(LbSSCC);
  freeandnil(LbEnvase);



  ListaCB.Clear;
  freeandnil(ListaCB);
  ListaSSCC.Clear;
  freeandnil(ListaSSCC);

  inherited;
end;

procedure TPanelArticuloFP02.FormClick(Sender: TObject);
var
  i: integer;
begin
  if not FPermitirSeleccionado then
    exit;
  if True then

  FIntFPScrollBox.PanelSeleccionado(Self);
  FIntFP02.articuloSeleccionado(lbCodigo.Caption);
  CambiarColorFuente;
  CambiarColorFondo;
end;

procedure TPanelArticuloFP02.inicializarCB;
var
  i: integer;
begin
  if assigned(ListaCB) then
    ListaCB.Free;
  ListaCB := FIntFP02.obtenerListaCB(FIdArticulo);
  ListaCB.Sort;
  ListaCB.Sorted := True;
end;

procedure TPanelArticuloFP02.inicializarSSCC;
var
  i: integer;
begin
  if assigned(ListaSSCC) then
    ListaSSCC.Free;
  ListaSSCC := FIntFP02.obtenerListaSSCC(FIdOrdenUbicacion);
  ListaSSCC.Sort;
  ListaSSCC.Sorted:=true;

end;

procedure TPanelArticuloFP02.JV1Click(Sender: TObject);
begin
  if FSeleccionado then
  begin
    //
  end
  else
    FORMCLICK(SENDER);



   if (jvled1.ColorOn=clred) or (advled1.Text='')then
   begin
      jvled1.ColorOn:=clgreen;
      advled1.Picture.LoadFromFile('.\logos\ledverde1000.png');
      advled1.Text:='.';
   end
   else
   begin
      jvled1.ColorOn:=clred;
      advled1.Picture.LoadFromFile('.\logos\ledrojo1000.png');
      advled1.Text:='';


   end;


end;

{procedure TPanelArticuloFP02.ComprobarCB(ACB:String);
var
  i: integer;
begin
  for I := 0 to listaCB. do

  if assigned(ListaCB) then
    ListaCB.Free;
  ListaCB := FIntFP02.obtenerListaCB(FIdArticulo);
  ListaCB.Sort;
  ListaCB.Sorted := True;
end;}


{ TScrollBoxArticulos }

constructor TScrollBoxArticulos.create(AOwner: TComponent);
begin
  inherited create(AOwner);
  FPanelArticulo := nil;
end;

destructor TScrollBoxArticulos.destroy;
begin

  inherited;
end;

procedure TScrollBoxArticulos.PanelSeleccionado(APanelArticulo: TFPPanel);
begin
  if FPanelArticulo <> APanelArticulo then
  begin
    if assigned(FPanelArticulo) then
      FPanelArticulo.seleccionado := false;

    FPanelArticulo := (APanelArticulo as TPanelArticuloFP02);
    if assigned(FPanelArticulo) then
      FPanelArticulo.seleccionado := True;
  end
  else if assigned(FPanelArticulo) then
    FPanelArticulo.seleccionado := not FPanelArticulo.seleccionado;
end;

procedure TPanelArticuloFP02.BtnMensajeEmergente;
begin
  // Formclick(lbDescripcion1);
   FintFP02.Mostrar_Mensaje_DirectoFp2('Detalle','OBSERVACIONES');

  if FSeleccionado then
  begin
    //
  end
  else
    FORMCLICK(SENDER);



   if (jvled1.ColorOn=clred) then
   begin
      jvled1.ColorOn:=clgreen;
      advled1.Picture.LoadFromFile('.\logos\ledverde1000.png');
      ADVled1.text:='.';
   end
   else
   begin
      jvled1.ColorOn:=clred;
      advled1.Picture.LoadFromFile('.\logos\ledrojo1000.png');


   end;

end;


end.
