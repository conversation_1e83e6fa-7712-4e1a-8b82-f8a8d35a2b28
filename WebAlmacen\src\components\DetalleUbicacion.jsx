import React, { useState, useRef, useEffect } from 'react';
import useScrollboxConfig from '../hooks/useScrollboxConfig';


export default function DetalleUbicacion({ data, onBack }) {
  const [input, setInput] = useState('');
  const inputRef = useRef(null);
  // Usar el hook para obtener la sección de configuración visual
  const { sectionConfig: config, error } = useScrollboxConfig('FP1_SCROLLBOX_ARTICULO');

  useEffect(() => {
    setTimeout(() => {
      inputRef.current && inputRef.current.focus();
    }, 100);
  }, []);

  // Utilidades para aplicar estilos dinámicos
  const getStyle = (prefix) => {
    const left = config && (config[`${prefix}_left`] || config[`${prefix}_Left`]) || undefined;
    const top = config && config[`${prefix}_linea`] || undefined;
    const fontSize = config && config[`${prefix}_font`] ? `${config[`${prefix}_font`]}pt` : undefined;
    let style = {};
    if (typeof left !== 'undefined') style.left = left;
    if (typeof top !== 'undefined') style.top = top;
    if (fontSize) style.fontSize = fontSize;
    return style;
  };

  // Mensajes de error/carga tras declarar hooks y utilidades
  if (error) return <div style={{color:'red',padding:24}}>Error cargando configuración visual: {error.message}</div>;
  if (!config) return <div style={{padding:24}}>Cargando configuración visual...</div>;


  return (
    <div className="detalle-ubicacion-wrapper">
      <div className="detalle-ubicacion-header">
        <button className="detalle-ubicacion-btn-back" onClick={onBack}>Volver</button>
        <h3>Detalle Ubicación</h3>
        <div style={{flex: 1}}></div>
      </div>
      <div className="detalle-ubicacion-input-box">
        <input
          id="detalle-ubicacion-input"
          ref={inputRef}
          type="text"
          placeholder="Instrucción o ubicación..."
          value={input}
          onChange={e => setInput(e.target.value)}
          autoFocus
        />
      </div>
      <div className="detalle-ubicacion-content" style={{position:'relative', minHeight: config.HEIGHT}}>
        <div className="detalle-ubicacion-card" style={config.HEIGHT ? {minHeight: config.HEIGHT} : {}}>
          {/* Campos principales con estilos dinámicos */}
          <div className="detalle-ubicacion-row detalle-ubicacion-row-header" style={getStyle('lbdescripcion1')}>
            <span style={getStyle('lbdescripcion1')}><b>Fecha:</b> {data?.fecha || '-'}</span>
            <span style={getStyle('lbdescripcion2')}><b>Orden Nº:</b> {data?.orden || '-'}</span>
            <span style={getStyle('lbdescripcion3')}><b>Op.:</b> {data?.op || '-'}</span>
            <span style={getStyle('lbdescripcion4')}><b>Líneas:</b> {data?.lineas || '-'}</span>
          </div>
          <div className="detalle-ubicacion-row detalle-ubicacion-row-main" style={getStyle('lbUDS')}>
            <span className="detalle-u" style={getStyle('lbUDS')}><b>U.{data?.uds || '-'}</b></span>
            <span className="detalle-cod" style={getStyle('lbCB')}><b>{data?.codigo || '-'}</b></span>
          </div>
          <div className="detalle-ubicacion-row detalle-desc" style={getStyle('lbdescripcion1')}>{data?.descripcion || '-'}</div>
          <div className="detalle-ubicacion-row detalle-medidas" style={getStyle('lbdescripcion2')}>{data?.medidas || '-'}</div>
          <div className="detalle-ubicacion-row detalle-sscc" style={getStyle('lbSSCC')}>{data?.sscc || '-'}</div>
          <div className="detalle-ubicacion-row detalle-c" style={getStyle('lbCB')}>
            <span style={getStyle('lbCB')}>C. {data?.c || '-'}</span>
            <span style={getStyle('lbCB')}>({data?.c2 || '-'})</span>
          </div>
          <div className="detalle-ubicacion-row detalle-prop" style={getStyle('lbLetraEstado')}>Prop.{data?.prop || '-'}</div>
        </div>
        <div className="detalle-ubicacion-instrucciones" style={{marginTop: 14}}>
          FP02-&gt;Introduzca ubicación<br/>
          FP02-&gt;Lea CB del artículo ó selecciónelo de la pantalla
        </div>
      </div>
    </div>
  );
}

