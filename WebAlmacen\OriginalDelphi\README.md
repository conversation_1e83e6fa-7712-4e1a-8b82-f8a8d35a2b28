# Proyecto WebAlmacen en Delphi

## Descripción General
WebAlmacen es una aplicación desarrollada en Delphi para la gestión de almacenes. El proyecto incluye funcionalidades avanzadas para la gestión de grids, impresión y manejo de configuraciones, con especial énfasis en la flexibilidad proporcionada por archivos de configuración como `MALASA.ini`.

## Estructura del Proyecto
- **uFPFormPrincipal.pas**: Lógica principal del formulario, configuración de grids y lógica de negocio central.
- **uFPPanel.pas**: Componente de panel personalizado.
- **uFrmAuxFP02.dfm**: Definiciones de formularios auxiliares.
- **MALASA.ini**: Archivo principal de configuración para columnas del grid y otros ajustes personalizables.

## Funcionalidades Clave
### 1. Configuración de Grids mediante MALASA.ini
La aplicación configura dinámicamente las columnas y propiedades del grid a partir del archivo `MALASA.ini`. Esto permite:
- Personalizar la visibilidad, orden, ancho y alineación de las columnas.
- Población dinámica de elementos de tipo combo box en columnas del grid.
- Ajuste del tamaño de fuente y otros aspectos visuales de los encabezados del grid.

#### Cómo se Carga y Utiliza MALASA.ini
- El archivo se carga en tiempo de ejecución mediante llamadas como:
  ```delphi
  CargarDesdeArchivo(VariablesPrograma.ObtenerInstancia.RutaINIColumnasGrid);
  ```
  Esto lee la configuración de las columnas del grid desde el archivo INI.
- El contenido se analiza y utiliza para crear y configurar las columnas del grid, incluyendo nombre de campo, alineación, ancho y elementos de lista desplegable (combo box).
- Ejemplo de código:
  ```delphi
  ArchivoGrid := TStringList.Create;
  ArchivoGrid.LoadFromFile(VariablesPrograma.ObtenerInstancia.RutaINIColumnasGrid);
  // ...analizar y aplicar la configuración a las columnas del grid
  ```
- Al dibujar el grid, la aplicación recorre la configuración analizada y aplica propiedades como alineación (`Centrado`, `Izquierda`, `Derecha`) y ancho, además de poblar los combos de las columnas.

### 2. Impresión y Otras Configuraciones
- La lógica de impresión también se adapta a los ajustes definidos en `MALASA.ini`, garantizando coherencia entre lo mostrado y lo impreso.

## Ejemplo de archivo MALASA.ini

A continuación se muestra un ejemplo típico de cómo estructurar el archivo `MALASA.ini` para definir las columnas de un grid:

```ini
[ColumnasGrid]
Columna1=NombreCampo1|Centrado|100|Opcion1,Opcion2
Columna2=NombreCampo2|Izquierda|80|
Columna3=NombreCampo3|Derecha|120|Activo,Inactivo,Otro
```
- **NombreCampoX**: Corresponde al nombre del campo de la base de datos o del dataset.
- **Alineación**: Puede ser `Centrado`, `Izquierda` o `Derecha`.
- **Ancho**: Valor numérico en píxeles.
- **Opciones**: (Opcional) Lista separada por comas para combos en esa columna.

## Ejemplo de uso en Delphi

A continuación, se muestra cómo cargar y aplicar la configuración de columnas desde el archivo INI en Delphi:

```delphi
var
  ArchivoGrid: TStringList;
begin
  ArchivoGrid := TStringList.Create;
  try
    // Cargar la configuración desde el archivo MALASA.ini
    ArchivoGrid.LoadFromFile(TVariablesPrograma.GetInstance.INIgridColumnas);

    // Recorrer cada línea para configurar las columnas del grid
    for var i := 0 to ArchivoGrid.Count - 1 do
    begin
      // Separar los valores por el carácter '|'
      var valores := SplitString(ArchivoGrid[i], '|');
      // valores[0]: Nombre del campo
      // valores[1]: Alineación
      // valores[2]: Ancho
      // valores[3]: Opciones (si existen)
      // Aquí se aplican las propiedades a cada columna del grid
    end;
  finally
    ArchivoGrid.Free;
  end;
end;
```

### Explicación paso a paso
1. **Definir el archivo MALASA.ini** con la sección `[ColumnasGrid]` y las columnas deseadas.
2. **Cargar el archivo INI** en la aplicación usando `TStringList.LoadFromFile`.
3. **Recorrer la configuración** y separar los valores usando el carácter `|`.
4. **Asignar las propiedades** (nombre, alineación, ancho y opciones) a cada columna del grid.

> **Nota:** Los cambios en el archivo `MALASA.ini` se aplican al reiniciar la aplicación.

## Personalización avanzada
- Puede agregar o quitar columnas, cambiar el orden, modificar la alineación o los valores de los combos simplemente editando el archivo INI.
- Si necesita definir combos diferentes para cada columna, simplemente agregue los valores separados por comas en la cuarta posición.

---

## Personalización avanzada del scrollbox de artículos (FP1)

La sección `[FP1_SCROLLBOX_ARTICULO]` del archivo `MALASA.ini` permite controlar exhaustivamente la disposición, tamaño, fuente y posición de cada etiqueta y campo en el scrollbox de artículos de la pantalla FP1. Cada clave corresponde a un aspecto visual o funcional de un elemento en esa interfaz.

### Archivo de ejemplo
Se recomienda consultar y partir del archivo [`EJEMPLO_FP1_SCROLLBOX_ARTICULO.ini`](./EJEMPLO_FP1_SCROLLBOX_ARTICULO.ini) incluido en este proyecto. Este archivo contiene todos los parámetros relevantes y valores recomendados para una personalización efectiva.

```ini
[FP1_SCROLLBOX_ARTICULO]
HEIGHT=87
HEIGHT_RESUMIDO=50
TOPLINEA1=1
TOPLINEA2=15
TOPLINEA3=29
TOPLINEA4=43
TOPLINEA5=56
lbSSCC_left=150
lbSSCC_linea=56
lbSSCC_font=8
lbTipoPedido_left=150
lbTipoPedido_linea=56
lbTipoPedido_font=8
lbLetraEstado_left=150
lbLetraEstado_linea=56
lbLetraEstado_font=8
lbLetraEstado_click=#1
lbenvase_left=150
lbenvase_linea=56
lbenvase_font=8
lbdescripcion1_Left=3
lbdescripcion1_linea=15
lbdescripcion1_font=8
lbdescripcion2_Left=3
lbdescripcion2_linea=29
lbdescripcion2_font=8
lbdescripcion3_Left=1000
lbdescripcion3_linea=29
lbdescripcion3_font=8
lbdescripcion4_Left=1000
lbdescripcion4_linea=29
lbdescripcion4_font=8
lbCB_Left=3
lbCB_linea=43
lbCB_font=8
lbpeso_Left=80
lbpeso_linea=56
lbpeso_font=8
lbUDS_left=150
lbUDS_linea=1
lbUDS_font=8
lbUnidadesTotales_left=150
lbUnidadesTotales_linea=1
lbUnidadesTotales_font=8
lblote_left=3
lblote_linea=56
lblote_font=8
lbubicacionActual_left=3
lbubicacionActual_linea=43
lbubicacionActual_font=8
```

### Explicación detallada de cada parámetro
- **HEIGHT / HEIGHT_RESUMIDO**: Altura total del scrollbox en modo normal y resumido.
- **TOPLINEA1-5**: Posición vertical (top, en píxeles) de cada línea de información, usada como referencia para ubicar los distintos labels y campos.
- **lbXXX_left**: Posición horizontal (en píxeles) del label o campo correspondiente.
- **lbXXX_linea**: Línea (posición vertical) donde se ubica el label, normalmente coincide con uno de los TOPLINEA.
- **lbXXX_font**: Tamaño de fuente del label.
- **lbLetraEstado_click**: Parámetro especial, puede indicar si el label es interactivo o tiene comportamiento especial.

### Cómo se usa en el código fuente
En el código Delphi, cada parámetro se lee y asigna así:
```delphi
FHeightScrollBox_FP1 := strtoint(IniFile.ReadString('FP1_SCROLLBOX_ARTICULO', 'HEIGHT', '87'));
FTopLinea1_FP1 := strtoint(IniFile.ReadString('FP1_SCROLLBOX_ARTICULO', 'TOPLINEA1', '1'));
FlbSSCC_left_FP1 := strtoint(IniFile.ReadString('FP1_SCROLLBOX_ARTICULO', 'lbSSCC_left', '150'));
FlbSSCC_linea_FP1 := strtoint(IniFile.ReadString('FP1_SCROLLBOX_ARTICULO', 'lbSSCC_linea', inttostr(FTopLinea5_FP1)));
FlbSSCC_font_FP1 := strtoint(IniFile.ReadString('FP1_SCROLLBOX_ARTICULO', 'lbSSCC_font', '8'));
// ...y así para cada parámetro
```
Luego, los valores se aplican a los controles visuales:
```delphi
LabelSSCC.Left := FlbSSCC_left_FP1;
LabelSSCC.Top := FlbSSCC_linea_FP1;
LabelSSCC.Font.Size := FlbSSCC_font_FP1;
```
Esto garantiza que cualquier cambio en el INI se refleje en la interfaz tras reiniciar la aplicación, sin recompilar.

### Mejores prácticas y recomendaciones
- **No uses líneas comentadas**: los valores deben estar activos (sin punto y coma al inicio) para que el sistema los lea y aplique.
- **Adapta los valores a tu resolución y preferencias**: prueba diferentes posiciones y tamaños para lograr la mejor experiencia visual.
- **Reinicia la aplicación después de modificar el INI** para que los cambios tengan efecto.
- **Consulta siempre el archivo de ejemplo** para no omitir ningún parámetro importante.

---

## Notas Adicionales
- La aplicación utiliza el patrón singleton (`TVariablesPrograma.GetInstance`) para acceder a las rutas y valores de configuración.
- Las funcionalidades de grid e impresión están estrechamente ligadas al archivo de configuración, lo que hace que la aplicación sea altamente adaptable a diferentes entornos o necesidades.

---

Para más detalles, consulte los comentarios en el código fuente de `uFPFormPrincipal.pas` relacionados con la creación y configuración del grid.
