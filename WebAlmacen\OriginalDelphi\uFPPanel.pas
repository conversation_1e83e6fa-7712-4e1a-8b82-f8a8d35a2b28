unit uFPPanel;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls,AdvPanel;

type
  TFPPanel = class(TAdvPanel)
  private
    { Private declarations }
  protected
    procedure CrearEtiqueta(AEtiqueta: TLabel;
      ALeft, ATop, AWidth, AHeight: Integer; ACaption: string;
      AAlignment: TAlignment);

  public
    { Public declarations }
    constructor Create(AOwner: TComponent); overload;
  end;

implementation

procedure TFPPanel.CrearEtiqueta(AEtiqueta: TLabel;
  ALeft, ATop, AWidth, AHeight: Integer; ACaption: string;
  AAlignment: TAlignment);
begin
  with AEtiqueta do
  begin
    Left := ALeft;
    Top := ATop;
    Width := AWidth;
    Height := AHeight;
    Caption := ACaption;
    Parent := Self;
    Alignment := AAlignment;
  end;
end;

constructor TFPPanel.Create(AOwner: TComponent);
begin
  inherited Create(AOwner);
  BevelKind := bkTile;
  BevelInner := bvNone;
  BevelOuter := bvNone;

end;

end.
