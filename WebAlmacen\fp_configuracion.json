{"CONFAPP": {"CIERRATERMINAL": "S", "NOMBRE_ALMACEN": "MALASA", "SUFIJO": "AX", "TRASLEERCBARTICULO": "SoloPocisionarse", "BOTONSPBDESGLOSEFP1": "AsignarDatosLLegadaFP1", "BOTONSPBDESGLOSEFP3": "DesbloquearDesgloseFP3", "REPARTIRPORPASILLOFP6": "N", "PermitirCambioUbicacionPropuesta_FP2": "S", "RELACIONUNIDADESPESO": "CantidadIgualPeso", "BOTONCABECERAFP1": "CrearDocumentoFP1", "ESTANDAR_ODETTE": "S", "CBUBICACION": "PPPPCCA", "Long_UBICACION_MANUAL": 7, "ACCIONTRASCONSULTAUBICACION": "CrearOrdenUbicacion", "VERSION_CAMBIAR_ESTADO": 2020, "IMPRESIONDESATENDIDA": "C:\\systemsoc\\Proyectos\\fastprocess\\test\\bin\\trabajos", "IMPRESIONIMPRESOS": "C:\\systemsoc\\Proyectos\\fastprocess\\test\\bin\\IMPRESOS", "IMPRESIONINFORMES": "C:\\systemsoc\\Proyectos\\fastprocess\\test\\bin\\Infmalasa"}, "TECLAS_FUNCION": {"F1": "#", "F2": "+", "F3": ",", "F4": "-", "F5": "*", "F6": "?", "TECLA_CONFIRMA_UBICACION": "?", "TECLAREFRESCAFP1": "'"}, "IMPRESORAS": {"LPT1": 771, "LPT2": 771, "LPT3": 771, "LPT4": 771, "ETIQUETAS_PACKING_LIST": "Etq_Packing_List", "CARTA_PORTE": "CartaPorte", "PDF": "", "OPEJOB": "S", "---0761') and (AInput <": "'0769')", "0771": "", "0772": "LPT2#PAC", "0773": "", "UBI0741": "LPT0|GODEXZPL_NOA5", "UBI0742": "LPT0|GK420d_NOA6", "UBI0743": "LPT0|ZT410_NOA7", "UBI0744": "LPT0|ZT410_NOA8", "UBI0745": "LPT0|", "UBI0746": "LPT0|", "UBI0747": "LPT0|", "UBI0748": "LPT0|", "UBI0749": "LPT0|", "UBI0770": "LPT0|", "UBI0771": "LPT0|PC42T_CMA8", "UBI0772": "LPT2|PC42T_CMA2", "UBI0773": "LPT3|PC42T_LAMELAS_P_ALTA", "UBI0774": "LPT4|GODEXZPL_LAMELAS", "UBI0775": "LPT5|GODEXZPL_CARBALLO", "UBI0776": "LPT6|GODEXZPL_CARBALLO2", "UBI0777": "LPT7|GODEXZPL_LAMELASOFICINA", "UBI0778": "LPT0|GK420d_NOA9", "UBI0780": "LPT0|PC42T_CMA7", "UBI0781": "LPT0|PC42T_CMA1", "UBI0782": "LPT0|PC42T_TAP1", "UBI0783": "LPT0|PC42T_CMA3", "UBI0784": "LPT0|PC42T_CMA4", "UBI0785": "LPT0|PC42T_CMA5", "UBI0786": "LPT0|PC42T_CMA6", "UBI0787": "LPT0|PC42T_FME1", "UBI0788": "LPT0|PC42T_FME2", "UBI0789": "LPT0|PC42T_FME3", "UBI0790": "LPT0|PC42T_FRC1", "UBI0791": "LPT0|PC42T_LOG1", "UBI0792": "LPT0|PC42T_LOG2", "UBI0793": "LPT0|PC42T_LOG3", "UBI0794": "LPT0|PC42T_LOG4", "UBI0795": "LPT0|PC42T_LOG5", "UBI0796": "LPT0|PC42T_NOA1", "UBI0797": "LPT0|PC42T_NOA2", "UBI0798": "LPT0|PC42T_NOA3", "UBI0799": "LPT0|PC42T_BAR1"}, "FUNCIONES": {"MASCARALOTEMANUAL": "DDMMYY", "MASCARALOTECBARRAS": "YYMMDD", "ComprobarUbicacionBarrasFp3": "S", "QListaDespaletizacionFP3": "select * from  (SELECT a.id , a.id_articulo,a.id_multiubicacion, b.numero, art.descripcion,substr(b.<PERSON><PERSON>,13,6) SSCC, sum(nvl(a.envases,0)) envase,round(sum(a.cantidad),2) cantidad ,b.tara_palet,b.tara_envase     from   fp03_pedidos_detALLE_L a, fp0_lotes b, fp0_articulos art    where id_pedidoS_detALLE= :PID_PEDIDOS_DET         and id_lote=b.id   and art.id=a.id_articulo         AND SSCC IS NOT NULL    group by substr(b.<PERSON><PERSON>,13,6),art.descripcion,a.id, b.numero, a.id_articulo,a.id_multiubicacion,b.tara_palet,b.tara_envase  )      where  (  cantidad <>0 or envase <>0 )", "IMPRIMEETIQUETASARTICULOS_FP2": "V_FP02_ETQ_SSCC_SALIDA_IMP", "ImprimeEtiquetasPrerecepcion_FP1": "N", "FILTRO_DEFECTO_FP1": "PC", "CambiarEstadoFp1_Procedure": "PKG01.CAMBIAR_ESTADO_ARTICULO", "Datos_Adicionales_Doc": "PKG01.DATOS_ADICIONALES_DOC", "ValidaCambioLoteFP1": "PKG01.VALIDA_CAMBIO_LOTE", "BorrarLoteRecibidoFP1": "PKG01.BORRAR_LOTE_RECIBIDO", "LISTADO_PEDIDOS_PENDIENTES_RECIBIR": "select * from V_FP01_PED_PDTES_RECIBIR where 1=1 and centro_operario=:CENTRO_OPERARIO", "FP1_DETALLE_PEDIDO": "v_fp01_ped_lineas_matriculas", "SELECT_FP1_DETALLE_PEDIDO": "select * FROM v_fp01_ped_lineas_matriculas  WHERE ID_PEDIDOSC=:ID_PEDIDO AND TIPO=:TIPO and ORDEN_ENTREGA=:ORDEN_ENTREGA", "INCREMENTAR_UNIDADES_fp1": "PKG01.INCREMENTAR_UNIDADES", "CERRAR_RECEPCION": "PKG01.CERRAR_RECEPCION", "ACTUALIZAR_LOTES": "PKG01.ACTUALIZAR_LOTE_PESQUERO", "GENERAR_ORDEN_UBICACION": "PKG01.GENERAR_ORDEN_UBICACION", "InsertaCabeceraFP1": "PKG01.INSERTA_CABECERA", "InsertaLineasFP1": "PKG01.INSERTA_LINEA_ARTICULO", "LOTE_FORMATOS": "Gitter|080|082|123|4322|6422|EUR", "FP1_INTRODUCIR_OBSERVACIONES": "NO", "IMPRESION_INDIRECTA": "S", "LISTADO_ORDENES_UBICACION": "PKG02.BUSCAR_ORDEN_UBICACION", "UBICAR_ORDEN_UBICACION": "PKG02.UBICAR_ORDEN_UBICACION", "IMPRIMEETIQUETASUBICACION_FP2": "V_FP02_ETQ_ORDENES_UBIC_IMP", "CrearOrdenesAutomaticas_ENTRADAS_FP2": "PKG02.Crear_ORDENES_Aut_ENT", "CrearOrdenesAutomaticas_INTERNAS_FP2": "PKG02.<PERSON><PERSON>r_ORDENES_Aut_INT", "RegularizaOrdenUbicacionFP2": "PKG02.REGULARIZAR_LINEA_ORDEN_UBIC", "AnularOrdenUbicacionFP2": "PKG02.ANULAR_LINEA_ORDEN_UBIC", "SELECT_FP2_AGRUPADO_ORDENES": "SELECT CODIGO as CODEMPRESA from Sesion.Username.GV_FP0_CENTROS_RESPONSABILIDAD where CODIGO<>'BORRAR' ORDER BY CODIGO", "LISTADO_ORDENES_UBICACION_CAB": "SELECT * FROM v_fp02_ordenes_ubicacion_cab WHERE 1=1 AND CENTRO_OPERARIO=:CENTRO_OPERARIO", "LISTADO_ORDENES_UBICACION_NPR": "SELECT * FROM v_fp02_ordenes_ubicacion_NPR WHERE 1=1 AND CENTRO_OPERARIO=:CENTRO_OPERARIO", "CLASE_ORDEN_FP2_INT": "ENC|", "INCREMENTAR_UNIDADES_fp3": "PKG03_VER3.INCREMENTAR_UNIDADES", "LINEAS_PEDIDOS_FP3": "QLineasPedidosMatricula", "FP3_DETALLE_PEDIDO": "v_fp03_pedidos_detalle_matricu", "UBICACION_EXTRAER_OPTIMA": "PKG03.OBTENER_UBIC_EXTRAER_MATRICULA", "DESCONTARMATRICULASOBLIGATORIASFp3": "S", "SelectCambioPedidoFp3": "Select * from v_fp03_pedidos_ordenes where 1=1 and fecha_fin is null and (id_operario is null or id_operario=:id_operario ) and fecha_anulacion is null", "SelectUbicacionesAlternativas_FP3": "SELECT * FROM v_fp03_ubicaciones_alternativa WHERE ID_aRTICULO=:AidArticulo", "ORDERBYSEGUNDAPISTOLAFP3": "order by DECODE(estado, 'P', 0,1),orden_previo desc,orden_previo2 , orden_pasillo,orden_preparacion ,pasillo asc, columna asc, altura asc,CANTIDAD_PREPARADA desc ,(CANTIDAD_PREPARADA - CANTIDAD_PREPARAR),CODIGO_ARTICULO  desc", "ENMATRICULASSEGUIRORDEN_FP03": "N", "TrabajarSinUbicacionesFp3": "S", "Prefijo_Pedido": "*", "INCREMENTAR_UNIDADES_fp5": "PKG05.INCREMENTAR_UNIDADES", "SoloPosicionarse_fp5": "S", "SelectDespaletizacionFp05": "select * from V_FP05_VARIAR_DESPALETIZACION  where id_inventario_det=Pid_inventario_det", "VariarDespaletizacionFP05": "FP05PKG03.VARIAR_DESPALETIZACION", "PROC_ESMATRICULA_FP09": "PKG09_TREN.PROC_ESMATRICULA", "LISTADO_ARTICULOS_FP98": "select * from v_fp98_articulos_ubicacion where 1=1", "LISTADO_UBICACIONES_FP98": "select * from v_fp98_articulos_ubicacion where 1=1"}, "MAPEO_PUERTOS": {"LPT1": "\\\\127.0.0.1\\ZEBRA_1", "LPT2": "\\\\127.0.0.1\\ZEBRA_1", "LPT3": "\\\\127.0.0.1\\ZEBRA_1", "LPT4": "", "LPT5": "", "LPT6": "", "LPT7": "", "LPT8": "", "LPT9": ""}, "MAPEO_PUERTOS_MALASA": {"LPT1": "", "LPT2": "\\\\127.0.0.1\\ZEBRA_1", "LPT3": "", "LPT4": "", "LPT5": "", "LPT6": "", "LPT7": "", "LPT8": "", "LPT9": ""}, "MAPEO_PUERTOS_05": {"LPT1": "", "LPT2": "\\\\127.0.0.1\\ZEBRA_1", "LPT3": "", "LPT4": "", "LPT5": "", "LPT6": "", "LPT7": "", "LPT8": "", "LPT9": ""}, "FP3_AYUDAS": {"NOMBREAYUDA1": "Cambiar <PERSON>", "AYUDA1": "ValidaEstadoArticulo", "NOMBREAYUDA2": "Despaletizacion", "AYUDA2": "Despaletizacion", "NOMBREAYUDA4": "<PERSON><PERSON><PERSON>", "AYUDA4": "CierreBultoPale", "NOMBREAYUDA5": "Genera Bajada PAC", "AYUDA5": "ValidaLineasAutomaticas", "NOMBREAYUDA6": "Aviso Cambio ubicacion", "AYUDA6": "ValidaUbicacionVacia", "NOMBREAYUDA7": "Pedidos a Preparar", "AYUDA7": "CambiarDePedido", "NOMBREAYUDA8": "Cierre Parcial", "AYUDA8": "ValidaCierrePedidoParcial", "NOMBREAYUDA9": "<PERSON><PERSON><PERSON>"}, "FP1_AYUDAS": {"NOMBREAYUDA1": "Cambiar <PERSON>", "AYUDA1": "ValidaEstadoArticulo", "AYUDA2": "CambiarLote", "NOMBREAYUDA3": "<PERSON><PERSON><PERSON>", "AYUDA3": "DesgloseLotes", "NOMBREAYUDA4": "", "AYUDA4": "", "NOMBREAYUDA5": "Etiq.Articulo", "AYUDA5": "<PERSON>mp<PERSON>ir<PERSON><PERSON><PERSON><PERSON>", "NOMBREAYUDA6": "", "AYUDA6": "<PERSON>mp<PERSON>ir<PERSON><PERSON><PERSON><PERSON>", "NOMBREAYUDA7": "Generar orden Ubicac.", "AYUDA7": "ImprimirOrdenUbicacion", "NOMBREAYUDA8": "Cierre Parcial", "AYUDA8": "CerrarRecepcionParcial", "NOMBREAYUDA9": "Cerrar recepci n", "AYUDA9": "CerrarRecepcion"}, "FP2_AYUDAS": {"NOMBREAYUDA1": "Consulta Ubicacion", "AYUDA1": "ConsultaUbicacion", "NOMBREAYUDA2": "<PERSON><PERSON>", "AYUDA2": "DivideOrden", "NOMBREAYUDA4": "Datos Adicionales SSCC", "AYUDA4": "DatosAdicionalesSSCC", "NOMBREAYUDA5": "Etiq.Articulo", "AYUDA5": "<PERSON>mp<PERSON>ir<PERSON><PERSON><PERSON><PERSON>", "NOMBREAYUDA6": "<PERSON>im<PERSON><PERSON><PERSON>", "AYUDA6": "Reimprimir_ORDUBIC", "NOMBREAYUDA7": "Unir Cod.Barras Montados", "AYUDA7": "GrabaNumeroSerie", "NOMBREAYUDA8": "Fusionar SSCC", "AYUDA8": "FUSIONARSSCC", "NOMBREAYUDA9": "Anular Linea", "AYUDA9": "AnularLinea", "LINEAADICIONAL0_Ayuda": ".0 Pasar a stock", "LINEAADICIONAL1_Ayuda": ".1 Confirma Proyecto", "LINEAADICIONAL2_Ayuda": ".9100 Crea SSCC de salida", "LINEAADICIONAL3_Ayuda": ".10 Imprime SSCC de salida", "LINEAADICIONAL4_Ayuda": ".11 Anula SSCC de salida", "LINEAADICIONAL8_Ayuda": "", "LINEAADICIONAL9_Ayuda": ""}, "FP22_AYUDAS": {"NOMBREAYUDA1": "", "AYUDA1": "", "NOMBREAYUDA2": "<PERSON><PERSON>", "AYUDA2": "DivideOrden", "NOMBREAYUDA3": "Revivir <PERSON>", "AYUDA3": "RevivirSSCC", "NOMBREAYUDA4": "Datos Adicionales SSCC", "AYUDA4": "DatosAdicionalesSSCC", "NOMBREAYUDA5": "Etiq.Articulo", "AYUDA5": "<PERSON>mp<PERSON>ir<PERSON><PERSON><PERSON><PERSON>", "NOMBREAYUDA6": "<PERSON>im<PERSON><PERSON><PERSON>", "AYUDA6": "Reimprimir_ORDUBIC", "NOMBREAYUDA7": "Regularizar Linea", "AYUDA7": "RegularizarLinea", "NOMBREAYUDA8": "", "AYUDA8": "", "NOMBREAYUDA9": "Anular Linea", "AYUDA9": "AnularLinea"}, "FP5_AYUDAS": {"NOMBREAYUDA1": "Cambiar <PERSON>", "AYUDA1": "ValidaEstadoArticulo", "NOMBREAYUDA2": "Despaletizacion", "AYUDA2": "Despaletizacion", "NOMBREAYUDA3": "", "AYUDA3": "", "NOMBREAYUDA4": "", "AYUDA4": "", "NOMBREAYUDA5": "Desglose Ubicaciones", "AYUDA5": "DesgloseUbicaciones", "NOMBREAYUDA6": "", "AYUDA6": "", "NOMBREAYUDA7": "", "AYUDA7": "", "NOMBREAYUDA8": "Cierre Parcial", "AYUDA8": "CerrarInventarioParcial", "NOMBREAYUDA9": "<PERSON><PERSON><PERSON>", "AYUDA9": "CerrarInventario"}, "FP6_AYUDAS": {"NOMBREAYUDA1": "Cambiar <PERSON>", "AYUDA1": "ValidaEstadoReparto", "NOMBREAYUDA2": "<PERSON><PERSON><PERSON>", "AYUDA2": "MostrarArticulos", "NOMBREAYUDA3": "", "AYUDA3": "", "NOMBREAYUDA4": "", "AYUDA4": "", "NOMBREAYUDA5": "Desglose Ubicaciones", "AYUDA5": "DesgloseUbicaciones", "NOMBREAYUDA6": "", "AYUDA6": "", "NOMBREAYUDA7": "", "AYUDA7": "", "NOMBREAYUDA8": "Cierre Parcial", "AYUDA8": "CerrarInventarioParcial", "NOMBREAYUDA9": "<PERSON><PERSON><PERSON>", "AYUDA9": "CerrarInventario"}, "FP98_AYUDAS": {"NOMBREAYUDA1": "", "AYUDA1": "", "NOMBREAYUDA2": "", "AYUDA2": "", "NOMBREAYUDA3": "", "AYUDA3": "", "NOMBREAYUDA4": "", "AYUDA4": "", "NOMBREAYUDA5": "Etiqueta Articulo", "AYUDA5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NOMBREAYUDA6": "", "AYUDA6": "", "NOMBREAYUDA7": "", "AYUDA7": "", "NOMBREAYUDA8": "", "AYUDA8": "", "NOMBREAYUDA9": "", "AYUDA9": ""}, "FP3_SCROLLBOX_ARTICULO": {"HEIGHT": 80, "lbcodigo_left": 1, "lbcodigo_font": 9, "TxCodigo": "", "lbubicacion_left": 125, "lbubicacion_linea": 60, "lbubicacion_font": 11, "TxUbicacion": "", "lbdescripcion1_left": 7, "lbdescripcion1_linea": 17, "lbdescripcion2_left": 10, "lbdescripcion2_linea": 29, "lbdescripcion2_font": 8, "lbpeso_left": 500, "lbUDS_left": 130, "lbUDS_linea": 1, "lbUDS_font": 11, "TXUDS": "", "lblote_left": 27, "lblote_linea": 60, "lblote_font": 8, "TxLote": "Lot:", "lbSSCC_left": 1, "lbSSCC_linea": 46, "lbSSCC_font": 9, "lblinea_left": 500, "lbLetraEstado_left": 1, "lbLetraEstado_linea": 44, "lbLetraEstado_font": 10, "lbLetraEstado_click": "#1", "lbenvase_left": 115, "lbenvase_linea": 39, "lbenvase_font": 11, "lbenvasesDisponibles_left": 190, "lbenvasesDisponibles_linea": 30, "lbenvasesDisponibles_font": 11, "TxEnvasesDisponibles": ".", "lbunidadesDisponibles_left": 190, "lbunidadesDisponibles_linea": 1, "lbunidadesDisponibles_font": 11, "TxunidadesDisponibles": "D.", "lbSSCC_Agrupacion_left": 500, "lbNombre_Agrupacion_left": 500, "lbNombre_Agrupacion_linea": 46, "lbNombre_Agrupacion_Font": 8, "JVLEDIndiqueAgrupacion_left": 10, "JVLEDIndiqueAgrupacion_linea": 44, "lbEstadoSSCC_left": 500, "lbEstadoSSCC_linea": 66, "TxEstadoSSCC": "Est.", "JVled1_left": 100, "JVled1_visible": "SiguienteUbicacion", "Led1_Click_FP3": "AparcaLinea", "TXlbpedido": "N", "lbCab_NombreBuque_left": 120, "lbCab_NombreBuque_linea": 34, "lbCab_NombreBuque_font": 11, "TXlbCab_NombreBuque": "Cj.", "lbCab_ContenedorSSCC_left": 500, "lbCab_ContenedorSSCC_linea": 39, "lbCab_ContenedorSSCC_font": 11, "TXlbCab_ContenedorSSCC": "SSCC:", "lbCab_NombreCliente_left": 7, "lbCab_NombreCliente_linea": 23, "lbCab_zona_left": 500, "lbCab_lineas_left": 10, "lbCab_lineas_linea": 37, "TXlbCab_lineas": "L.:", "lbCab_numpedido_left": 22, "lbCab_numpedido_linea": 10, "lbCab_numpedido_font": 8, "TXlbCab_numpedido": "", "lbCab_horacomienzo_left": 500, "lbCab_horacomienzo_linea": 10, "lbCab_horacomienzo_font": 8, "TXlbCab_horacomienzo": "", "Txlbpedido": "N"}, "FP1_SCROLLBOX_ARTICULO": {"HEIGHT": 80, "lbubicacionPropuesta_left": 130, "lbubicacionPropuesta_linea": 1, "TxUbicacionPropuesta": "", "lbubicacionActual_left": 2000, "lbubicacionActual_linea": 80, "lbubicacionActual_font": 8, "TxUbicacionActual": "U.", "lbdescripcion1_left": 1, "lbdescripcion1_linea": 15, "lbdescripcion1_font": 8, "lbdescripcion2_left": 1, "lbdescripcion2_linea": 26, "lbdescripcion2_font": 8, "lbdescripcion3_left": 1, "lbdescripcion3_linea": 37, "lbdescripcion3_font": 8, "lbdescripcion4_left": 1, "lbdescripcion4_linea": 48, "lbdescripcion4_font": 8, "lbCB_left": 140, "lbCB_linea": 80, "lbCB_font": 8, "lbpeso_left": 5000, "lbpeso_linea": 63, "lbUDS_left": 180, "lbUDS_linea": 60, "lbUDS_font": 9, "TXUDS": "", "lblote_left": 5000, "lblote_linea": 85, "lblote_font": 11, "lbenvase_left": 4000, "lbenvase_linea": 60, "lbenvase_font": 10, "TxEnvase": "Env.", "lbSSCC_left": 20, "lbSSCC_linea": 60, "lbSSCC_font": 9, "TxSSCC": "", "lbLetraEstado_left": 1, "lbLetraEstado_linea": 55, "lbLetraEstado_font": 12, "lbTipoPedido_left": 300, "lbFormatoEnvasado_left": 150, "lbFormatoEnvasado_linea": 60, "lbFormatoEnvasado_font": 9, "CAB_Led1_Left_FP1": 500, "led1_Left_FP1": 500, "led1_visible_FP1": "N", "led1_click_FP1": "IntegracionWS", "led2_Left_FP1": 500, "led2_visible_FP1": "N", "led2_click_FP1": "BtnImprimirCantidadRecibida_CD", "lbCab_lineas_left": 1, "lbCab_lineas_linea": 40, "lbCab_fecha_left": 500}, "FP2_SCROLLBOX_ARTICULO": {"HEIGHT": 98, "lbcodigo_left": 85, "lbcodigo_linea": 0, "lbcodigo_font": 11, "TxCodigo": ".", "lbcodigoBarras_left": 1, "lbcodigoBarras_linea": 28, "lbcodigoBarras_font": 9, "TxCodigoBarras": ".", "lbReferenciaProveedor_left": 1, "lbReferenciaProveedor_linea": 57, "lbReferenciaProveedor_font": 7, "TxReferenciaProveedor": "Ref.", "lbUDS_left": 1, "lbUDS_linea": 0, "lbUDS_font": 11, "TxUDS": "U.", "lbdescripcion3_left": 1, "lbdescripcion3_linea": 69, "lbdescripcion3_font": 8, "lbdescripcion3_color": "clBlue", "lbdescripcion4_left": 1, "lbdescripcion4_linea": 83, "lbdescripcion4_font": 8, "lbStatusSSCC_Left": 500, "lbStatusSSCC_linea": 69, "lbStatusSSCC_font": 8, "lbdescripcion1_linea": 15, "lbdescripcion1_font": 8, "lbdescripcion2_left": 120, "lbdescripcion2_linea": 28, "lbdescripcion2_font": 8, "lbdescripcion2_color": "clgreen", "lbubicacionPropuesta_left": 3, "lbubicacionPropuesta_linea": 42, "TXubicacionPropuesta": "Ub.", "lbubicacionActual_left": 105, "lbubicacionActual_linea": 42, "TXubicacionActual": "Prop.", "lbSSCC_left": 2, "lbSSCC_linea": 29, "lbenvase_left": 1300, "lbenvase_linea": 29, "TxEnvase": "C.", "lblote_left": 5000, "lblote_linea": 0, "TxLote": "Lot.", "lbObservaciones_left": 170, "lbObservaciones_linea": 10, "lbObservaciones_font": 10, "TxlbObservaciones": "B.", "BtnMapa_left": 1500, "BtnMapa_linea": 1, "JVled1_left": 1000}, "FP5_SCROLLBOX_ARTICULO": {"HEIGHT": 62, "TOPLINEA1": 1, "TOPLINEA2": 15, "TOPLINEA3": 29, "TOPLINEA4": 42, "TOPLINEA5": 57, "lbCodigo_left": 2, "lbCodigo_font": 9, "TxCodigo": "Cod.", "lbSSCC_left": 2, "lbSSCC_linea": 31, "lbSSCC_font": 9, "TxSSCC": "Mat.", "lbdescripcion1_left": 3, "lbdescripcion1_linea": 18, "lbpeso_left": 500, "lbUDS_left": 155, "lbUDS_font": 10, "TxUDS": "ST:", "lblote_left": 30, "lblote_linea": 43, "lbenvase_left": 150, "lbenvase_linea": 29, "lbenvase_font": 11, "TxEnvase": "Env.", "lbubicacion_left": 63, "lbubicacion_linea": 1, "lbubicacion_font": 10, "Txubicacion": "Ubic:", "lbLetraEstado_left": 1, "lbLetraEstado_linea": 42, "lbLetraEstado_font": 12, "lbLetraEstado_click": "#1", "led1_Left_FP5": 500}, "FP6_SCROLLBOX_ARTICULO": {"HEIGHT": 20, "TOPLINEA1": 0, "lbcodigo_left": 250, "TxCodigo": "", "lbcodDestino_left": 3, "lbcodDestino_linea": 0, "lbcodDestino_font": 12, "TxCodDestino": "Dest.", "lbDestino_left": 250, "lbDestino_linea": 0, "lbDestino_font": 12, "lbubicacion_left": 250, "lbpeso_left": 140, "lbpeso_linea": 0, "lbpeso_font": 8, "lbUDS_left": 40, "lbUDS_linea": 0, "lbUDS_font": 8, "lbenvase_left": 90, "lbenvase_linea": 0, "led1_visible_FP6": "S", "led1_left_FP6": 20, "led1_linea_FP6": 0}, "FP9_SCROLLBOX_ARTICULO": {"HEIGHT": 30, "TOPLINEA1": 1, "TOPLINEA2": 15, "TOPLINEA3": 29, "TOPLINEA4": 42, "TOPLINEA5": 57, "lbCodigo_left": 35, "lbCodigo_linea": 1, "lbCodigo_font": 9, "TxCodigo": "Cod.", "lbSSCC_left": 1, "lbSSCC_linea": 15, "lbSSCC_font": 9, "TxSSCC": "Mat.", "lbdescripcion1_left": 1500, "lbdescripcion1_linea": 18, "lbdescripcion2_left": 1500, "lbpeso_left": 1500, "lbUDS_left": 1500, "lbUDS_font": 10, "TxUDS": "ST:", "lblote_left": 3000, "lblote_linea": 43, "lbenvase_left": 130, "lbenvase_linea": 1, "lbenvase_font": 11, "TxEnvase": "Env.", "lbubicacion_left": 130, "lbubicacion_linea": 15, "lbubicacion_font": 10, "Txubicacion": "", "lbLetraEstado_left": 1, "lbLetraEstado_linea": 42, "lbLetraEstado_font": 12, "lbLetraEstado_click": "#1", "led1_Left_FP9": 500, "lbimg01_left": 1, "lbimg02_left": 200, "lbCab_numero_left": 10, "lbCab_numero_linea": 22, "lbCab_numero_font": 8, "TXlbCab_numero": "Tren:", "lbCab_fecha_left": 500, "lbCab_fecha_linea": 22, "lbCab_fecha_font": 8, "TXlbCab_fecha": "L.", "lbCab_lineas_left": 500, "lbCab_lineas_linea": 22, "lbCab_lineas_font": 8, "TXlbCab_lineas": "L.", "CAB_Led1_Left_FP9": 60}, "FP98_SCROLLBOX_ARTICULO": {"HEIGHT": 92, "TOPLINEA1": 1, "TOPLINEA2": 15, "TOPLINEA3": 29, "TOPLINEA4": 45, "TOPLINEA5": 58, "lbcodigo_left": 1, "lbcodigo_linea": 15, "lbcodigo_font": 11, "lbdescripcion1_left": 1, "lbdescripcion1_linea": 52, "TXDescripcion1": "", "lbAnagrama_left": 1, "TXAnagrama": "Ubic.", "lbEstado_left": 145, "TXEstado": "Estado", "lbTipoEtiqueta_left": 500, "TXTipoEtiqueta": "", "lbLoteNumero_left": 500, "lbLoteNumero_linea": 42, "TXLoteNumero": "Lote", "lbLoteDoc_left": 10, "lbLoteDoc_linea": 42, "TXLoteDoc": "Obra", "lbSSCC_left": 5, "lbSSCC_linea": 60, "lbSSCC_font": 11, "TxSSCC": "SSCC", "lbCantidad_left": 145, "lbCantidad_linea": 47, "lbCantidad_font": 11, "TxCantidad": "Uds.", "lbEnvase_left": 500}, "FP1_DOCUMENTOS": {"Campo_Filtro_Cabecera_FP1": "TIPODOC", "DOCUMENTO1": "00.- Entrada Ciega", "DOCUMENTO1_CIEGO": "S", "DOCUMENTO1_MODOANADIR_CREATE": "OFF.BMP", "DOCUMENTO1_CONTADOR": "S", "DOCUMENTO2": "CM", "DOCUMENTO2_CIEGO": "S", "DOCUMENTO2_MODOANADIR_CREATE": "OFF.BMP", "DOCUMENTO2_CONTADOR": "S", "DOCUMENTO3": "PC", "DOCUMENTO3_CIEGO": "S", "DOCUMENTO3_MODOANADIR_CREATE": "OFF.BMP", "DOCUMENTO3_CONTADOR": "S", "DOCUMENTO4": "PCD.-Devoluciones", "DOCUMENTO4_CIEGO": "S", "DOCUMENTO4_MODOANADIR_CREATE": "OFF.BMP", "DOCUMENTO4_CONTADOR": "S"}, "FP3_DOCUMENTOS": {"DOCUMENTO1": 0, "DOCUMENTO1_CIEGO": "N"}, "LETRAS_AUXILIARES": {"LETRAS_AUXILIARES1": "A", "LETRAS_AUXILIARES2": "B", "LETRAS_AUXILIARES3": "C", "LETRAS_AUXILIARES4": "D", "LETRAS_AUXILIARES5": "E", "LETRAS_AUXILIARES6": "F"}, "CBARRAS_DEMO": {"DataGramaPesoPalet": "20*321,56232709031", "ARTICULO": "PEE0001", "MATRICULA WIP": "91..1101160", "UBICACIONES": "02-01-001-04 //020100104"}}