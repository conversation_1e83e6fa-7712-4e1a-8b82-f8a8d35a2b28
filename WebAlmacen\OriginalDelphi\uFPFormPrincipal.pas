﻿unit uFPFormPrincipal;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, uFPFrmAuxLogin, uFPFormBasico, uFPFormAuxBasico,
  uDMBasico, intFormAux2FormPpal, StrUtils, Mask, Uni, Buttons,CRGrid,ShellApi,
  System.Win.ScktComp,dateutils, JvExControls,
   JvLED, AdvUtil, Vcl.Grids,
  AdvObj, BaseGrid, AdvGrid,
   DBAdvGrid,advpanel,
  Vcl.Touch.GestureMgr,Gestures;

type
  TFormPrincipal = class(TFormBasico, IFormPpal)
    BtnMenu: TButton;
    BtnInicio: TButton;
    Image1: TImage;
    PanelTeclasFuncion: TPanel;
    BtnF2: TButton;
    BtnF1: TButton;
    BtnF3: TButton;
    BtnF4: TButton;
    BTNf5: TButton;
    BtnF6: TButton;
    PanelEstado: TPanel;
    LabelEstado: TLabel;
    ClientSocketBAscula: TClientSocket;
    BTNACCRapido2: TButton;
    GestureManagerPPAL: TGestureManager;
    TimerExpulsores: TTimer;
    timerTIMEOUT_COMPROBACION: TTimer;
    ListBoxComprobacion: TListBox;
    listboxlecturas: TListBox;
    procedure FormCreate(Sender: TObject);
    procedure BtnResolucionClick(Sender: TObject);
    procedure BtnMenuClick(Sender: TObject);
    procedure BtnSalirClick(Sender: TObject);
    procedure EditInputKeyUp(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure MemoAyudaEnter(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure EditInputChange(Sender: TObject);
    procedure EditInputKeyPress(Sender: TObject; var Key: Char);
    procedure BtnF1Click(Sender: TObject);
    procedure BtnF3Click(Sender: TObject);
    procedure BtnF4Click(Sender: TObject);
    procedure BtnF2Click(Sender: TObject);
    procedure BTNf5Click(Sender: TObject);
    procedure BtnF6Click(Sender: TObject);
    procedure BtnInicioClick(Sender: TObject);

    procedure EditInputKeyWearable(Tecla:String);
    procedure LabelEstadoClick(Sender: TObject);
    procedure ClientSocketBAsculaConnect(Sender: TObject;
      Socket: TCustomWinSocket);
    procedure ClientSocketBAsculaError(Sender: TObject;
      Socket: TCustomWinSocket; ErrorEvent: TErrorEvent;
      var ErrorCode: Integer);
    procedure ClientSocketBAsculaRead(Sender: TObject;
      Socket: TCustomWinSocket);
    procedure MemoAyudaGesture(Sender: TObject;
      const EventInfo: TGestureEventInfo; var Handled: Boolean);

  private
    FrmAuxLogon: TFormAuxBasico;
    FrmAuxBasico: TFormAuxBasico;
    FrmTeclado:TFormAuxBasico;


    FMensajeFlujo: String;
    FOpcMenu: String;
    AAccionActiva: String;
    procedure BloqueaControles;
    function  ShutDownWindows(Flag: Word): Boolean;
    procedure CambiaResolucion(HeightNuevo, HeightActual: Integer);
    procedure EscribirMensajeAyuda(AMensaje, AMenu: String);
    procedure ActivarEditInput(Estado: TEstadosEdit);
    procedure CerrarAplicacion;
    procedure limpiarEditInput;
    procedure limpiarPanelEstadoProceso(cadena:string);
    // procedure mostrarPantallaFP(TipoPantalla: TTiposPantallas; AInput: string);
    procedure mostrarPantallaFP(AAccion, AOpcion: String);
    procedure VolverMenu;
    function  CompruebaAlmacenInput(AInput: String; AOpcMenu: String): Boolean;
    procedure CambiarCaptionBotonInicio(ACaption: String);
    procedure EscribeInput(AValor: String);
    procedure AlmacenaMensajeFlujo(AMensaje, AOpcMenu: String);
    procedure focoEditInput;


    { Private declarations }
  public
  FintFP01: IFP01;
    DataMBasico: TDataMBasico;

    procedure CreaFormAuxiliar;
    procedure ActivaControles;
    procedure Split(Delimiter: Char; Str: string; ListOfStrings: TStrings) ;
    function  separaCadenas (CadenaOrigen:string;  SalidaList: TStringList):String;

    procedure CreaColumnasGrid (CRdbGrid:TCRdbGrid;Busqueda:String);
    procedure CreaADVColumnasGrid (Grid:TDBAdvGrid;Origen:String;ClearCombos:Boolean);
    procedure ActivaTecladoTactil(Mensaje:String);
    procedure DesactivaTecladoTactil(Mensaje:String);
    function  Es_Fecha(Cadena_Origen:string):String;

    procedure CreaSucursales(Sender:Tobject);
    procedure UsaSucursales(Sender:Tobject;info2:string);

    function  NumcaracteresAlfabeticos (CadenaOrigen:string):Integer;
    function  FindMyComponent(Parent: TComponent; Name: string): TComponent;
    procedure CustomOnClick(Sender: TObject);
    Procedure GrabaOrdenImpresionOperario (Impresora:string);
   function  IMPRIMETRABAJO(STringLISTFCIHEROS:TStringList;OPERARIO, NOMBREFICHERO,TipoPapel,vImpresora,numeroetiquetas: string): string;
   Procedure ImprimeTrabajo_IntroduceLogo(AplantillaLoGO,AFichero:String);

    procedure Escribirmemolog(AMensaje:String);
    function  DameBasculaTCPIP(NumBascula:String):String;
    Function  IS_ADV_GridHeaderColumn (Grid:TDBAdvGrid):Boolean;
    procedure LimpiarCola;
    Procedure DameDatosAdicionales(ASSCC:string;var  APedidoVenta, AOrdenPicking, ANombre_Cliente:string);

function DAmeNumeroItems:integer;
    Procedure BOrraMatricula(AmatriculaBorrar,ACadenaCB_PdteValidar:String);
    Procedure BOrraMatriculaComprobacion(AmatriculaBorrar:String);
function DAmeProximaMatricula:String;
procedure AnadeBultoalaCola(Amatricula, Apedido, ANombre_Cliente:String);
    function DAmeProximaMatriculaComprobacion:String;
  function Convierte91 (ACadena:String):String;
         procedure ArrancaTarjetaMesurex;
        procedure TarjetaMesurex_Enviarunbit(Encender:integer);
        procedure ActivaExpulsor(Aexpulsor:integer);
        procedure MostrarPantallaCambiarExpulsores;
        procedure REINICIARExpulsor9;
        procedure UnirArchivoNEW(Lista: TStringList;sArchivoFinal: String);
        procedure Arranca_Thread_Lector_IN;
        procedure Arranca_Thread_Lector_OUT;
        procedure Showalert(Amensaje:String);
        procedure ActivaExpulsor7;
        procedure DesactivaTodosLosExpulsores;
        procedure Arranca_PARA_Thread_TTimerComprobacion(Arranca,Identificador:String);
       function SpoolFile(const FileName, PrinterName: string): Integer;
       function GetLabelPrinterIndex(AnombreImprsora:String): Integer;

       procedure PrintTextFroMservice(Aimpresora,Afilename:String);

    { Public declarations }
  end;

const
  TAMBASEFORM: Integer = 320;

resourcestring
  RS_ErrorCodAlm =
    'El código de almacén de la etiqueta no conicide con su almacén';
  /// RS_InputVacio = 'Debe introducir un valor';

var
  FormPrincipal: TFormPrincipal;

implementation

{$R *.dfm}

uses
  Printers,WinSpool,uVariablesPrograma, uFrmAuxFP0, uFrmAuxFP3, uFrmAuxFP01, uFrmAuxFP02, uFrmAuxFP05,uFrmAuxFP06, uFrmAuxFP07 ,uFrmAuxFP08,
  uFrmAuxFP09, uFrmAuxFP31,
  uFrmAuxFP90 ,uFrmAuxFP97,uFrmAuxFP98, uFrmAuxFP99,
  uFrmColaImpresionFP3,CodBarrasValidaciones,uFPTeclado;


function GetUserName : String;
 var
    pcUser   : PChar;
    dwUSize : DWORD;
 begin
    dwUSize := 21;
    GetMem( pcUser, dwUSize );
    try
       if Windows.GetUserName( pcUser, dwUSize ) then
          Result := pcUser
    finally
       FreeMem( pcUser );
    end;
end;

  function GetFilemODIFYTime(FileName: string): TDateTime;
var
   SearchRec: TSearchRec;
   LocalFileTime: TFileTime;
  T: Integer;
begin
  if FindFirst(Filename,faAnyFile,SearchRec) = 0 then
 begin
    FileTimeToLocalFileTime(SearchRec.FindData.ftLastWriteTime, LocalFileTime);
    FileTimeToDosDateTime(LocalFileTime, LongRec(T).Hi, LongRec(T).Lo);
    Result:= FileDateToDateTime(T)
  end else
  Result:= 0;
  FindClose(SearchRec);
end;


function RellenaCAdena(cadena:string;longitud:Integer;relleno,posicion:string) : string;
var
    cTemp:string;
    n, nVeces:Integer;
begin
{Parametros :
cadena = cadena inicial a tratar
longitud = longitud final de la cadena
relleno = caracter de relleno
posicion = si (D ó d) rellena por la derecha, sino por la izquierda de la cadena pasada en CADENA.
}
    cTemp := cadena;
    nVeces := longitud - length(cTemp);
    for n := 1 to nVeces do
    begin
    if ((posicion = 'D') or (posicion = 'd')) then
       cTemp := cTemp+relleno
    else
      cTemp := relleno+cTemp;
    end;

  Result := cTemp;
end;

procedure TFormPrincipal.FormCreate(Sender: TObject);
var
  resolucionPAD:String;
begin
  inherited;

  //TcontrolCB.GetInstance.ToggleCapsLock;


  try
    BtnMenu.Caption := 'Salir';
    BtnMenu.OnClick := BtnSalirClick;

    DataMBasico := TDataMBasico.Create(Self);

//2034 14052014   TVariablesPrograma.GetInstance.CambiaResolucion(Self.ClientHeight,

     TVariablesPrograma.GetInstance.CambiaResolucion(strtoint(TVariablesPrograma.GetInstance.Resolucion),
      Self.Height);

      if Self.ClientHeight<strtoint(TVariablesPrograma.GetInstance.Resolucion) then
       cambiaresolucion (strtoint(TVariablesPrograma.GetInstance.Resolucion),320);


      resolucionPAD:=TvariablesPrograma.GetInstance.resolucion.PadLeft(5, '0');

     if  resolucionPAD >='00640' then
     begin
           if not fileexists('.\logos\'+TVariablesPrograma.GetInstance.Logo) then
             begin
             showmessage(resolucionPAD+'//'+'.\logos\'+TVariablesPrograma.GetInstance.Logo);
           end ;

        if  TVariablesPrograma.GetInstance.Resolucion >='640' then
          IMAGE1.Picture.LoadFromFile('.\logos\'+TVariablesPrograma.GetInstance.Logo);
     end;


    CreaFormAuxiliar;
    ActivarEditInput(TEE_NUMERICO);
    if (TVariablesPrograma.GetInstance.BtnF1<>'') or
       (TVariablesPrograma.GetInstance.BtnF2<>'') or
       (TVariablesPrograma.GetInstance.BtnF3<>'') or
       (TVariablesPrograma.GetInstance.BtnF4<>'') or
       (TVariablesPrograma.GetInstance.BtnF5<>'') or
       (TVariablesPrograma.GetInstance.BtnF6<>'') then
     begin
    if TVariablesPrograma.GetInstance.BtnF1<>'' then
    begin
      btnF1.Caption:=TVariablesPrograma.GetInstance.BtnF1;
      btnF1.Visible:=true;
    end;
    if TVariablesPrograma.GetInstance.BtnF2<>'' then
    begin
      btnF2.Caption:=TVariablesPrograma.GetInstance.BtnF2;
      btnF2.Visible:=true;
    end;
    if TVariablesPrograma.GetInstance.BtnF3<>'' then
    begin
      btnF3.Caption:=TVariablesPrograma.GetInstance.BtnF3;
      btnF3.Visible:=true;
    end;
    if TVariablesPrograma.GetInstance.BtnF4<>'' then
    begin
      btnF4.Caption:=TVariablesPrograma.GetInstance.BtnF4;
      btnF4.Visible:=true;
    end;
    if TVariablesPrograma.GetInstance.BtnF5<>'' then
    begin
      btnF5.Caption:=TVariablesPrograma.GetInstance.BtnF5;
      btnF5.Visible:=true;
    end;
    if TVariablesPrograma.GetInstance.BtnF6<>'' then
    begin
      btnF6.Caption:=TVariablesPrograma.GetInstance.BtnF6;
      btnF6.Visible:=true;
    end;

     end
     else
     begin
         PanelTeclasFuncion.Visible:=False;
     end;

     Memoayuda.Font.Size:=strtoint(TVariablesPrograma.GetInstance.FontMensajes);

	    if TvariablesPrograma.GetInstance.Resolucion='800' then //PARA QUE SE VEA EN EL EDA 50K
       FormPrincipal.Height := Screen.Height-2;

       if pos(uppercase('TEST'), uppercase(Application.ExeName))>0 then
       begin
           PanelBotones.Color:= $00434ADC   ;
       end;

       if TVariablesPrograma.GetInstance.NOMBRE_ALMACEN='EZPELETA' then
       begin
           PanelTeclasFuncion.Height:=85;
          btnf1.HEIGHt:=  45;
          btnf2.HEIGHt:=  45;
          btnf3.HEIGHt:=  45;
          btnf4.HEIGHt:=  45;
          btnf5.HEIGHt:=  45;
          btnf6.HEIGHt:=  45;
       end ;


//      if TVariablesPrograma.GetInstance.Resolucion = '800' then
  //    formprincipal.ClientHeight:=1000;


  except
    on e: Exception do
      EscribirMensajeAyuda(e.Message, '00');
  end;


end;

procedure TFormPrincipal.FormDestroy(Sender: TObject);
begin
  inherited;
   if assigned(FrmAuxLogon) then
    freeandnil(FrmAuxLogon);
  if assigned(FrmAuxBasico) then
    freeandnil(FrmAuxBasico);
end;

procedure TFormPrincipal.LabelEstadoClick(Sender: TObject);
var
   formControl: TFormAuxBasico;
begin
  inherited;
  formControl := FrmAuxBasico;

if assigned(Frmteclado) then
begin

   FormControl.EjecutaTecla(VK_INSERT, EditInput.Text);
   freeandnil(Frmteclado)
end
else
 begin
	//09/12/20018 si es Android el teclado tactil no se usa
  if TVariablesPrograma.GetInstance.TECLADOTACTIL ='N' then
  //  TECLADOTACTIL=NTrue then
  else
  begin


  Frmteclado := TFormAuxTeclado.Create(Self);
  Frmteclado.Parent := Self.PanelCliente;
  Frmteclado.Align :=Alcustom ;//Self.PanelCliente.Height-117;

    Frmteclado.Show;

    Frmteclado.Top:=PanelCliente.Top+PanelCliente.height-210;
    //Frmteclado.height;//117 para wearable;
    Frmteclado.left:=3; //20 para wearable?

    FrmTeclado.ScaleBy(strtoint(TVariablesPrograma.GetInstance.Resolucion) ,
      TAMBASEFORM);
  end;
 end;

end;

procedure TFormPrincipal.ActivaTecladoTactil(mensaje:String);

begin
  if Assigned(frmteclado) then
   freeandnil(frmteclado);

  EscribirMensajeAyuda(mensaje,'');
  LabelEstadoClick(Self);
end;


procedure TFormPrincipal.DesactivaTecladoTactil(mensaje:String);

begin
  if Assigned(frmteclado) then
   freeandnil(frmteclado);
end;


procedure TFormPrincipal.limpiarEditInput;
begin
  EditInput.Clear;
  //PanelEstado.
  if EditInput.CanFocus then
    EditInput.SetFocus;
end;
procedure TFormPrincipal.limpiarPanelEstadoProceso(cadena:string);
begin
  labelestado.caption:=cadena;
end;




procedure TFormPrincipal.MemoAyudaEnter(Sender: TObject);
begin
  inherited;
  if EditInput.CanFocus then
     EditInput.SetFocus;
end;



procedure TFormPrincipal.mostrarPantallaFP(AAccion, AOpcion: String);
// TipoPantalla: TTiposPantallas;
// AInput: string);
var
 vdoble:double;
begin

        BtnMenu.Caption := 'MENÚ';
        BtnMenu.OnClick := BtnMenuClick;
        BtnInicio.Visible := True;
        FrmAuxLogon.Visible := False;
         AAccionActiva:=Aaccion;
      //  if AAccion = 'MenuSecun' then
      //  begin
      //    if not assigned(FrmAuxBasico) then
      //      FrmAuxBasico := TFrmAuxFP3.Create(Self, DataMBasico.Sesion, Self, AOpcion)
      //    else if not(FrmAuxBasico is TFrmAuxFP3) then
      //    begin
      //      FrmAuxBasico.Free;
      //      FrmAuxBasico := TFrmAuxFP3.Create(Self, DataMBasico.Sesion, Self,
      //        AOpcion);
      //    end;
      //  end
  if AAccion = 'MantArt' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP99.Create(Self, DataMBasico.Sesion, Self, AOpcion)
    else
    if not(FrmAuxBasico is TFrmAuxFP99) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP99.Create(Self, DataMBasico.Sesion, Self,
        AOpcion);
      //FrmAuxBasico.ScaleBy(TVariablesPrograma.GetInstance.HeightNuevo, TAMBASEFORM)
    end;

  end
  else
  if AAccion = 'MantUbi' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP98.Create(Self, DataMBasico.Sesion, Self, AOpcion)
    else if not(FrmAuxBasico is TFrmAuxFP98) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP98.Create(Self, DataMBasico.Sesion, Self,
        AOpcion);
     // FrmAuxBasico.ScaleBy(TVariablesPrograma.GetInstance.HeightNuevo, TAMBASEFORM)
    end;
  end

  else if AAccion = 'MantArt' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP01.Create(Self, DataMBasico.Sesion, Self, AOpcion,'')
    else if not(FrmAuxBasico is TFrmAuxFP01) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP01.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'');
     //UÑTIMO FrmAuxBasico.ScaleBy(TVariablesPrograma.GetInstance.HeightNuevo, TAMBASEFORM)
    end;
  end

  else if AAccion = 'RecepMercan' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP01.Create(Self, DataMBasico.Sesion, Self, AOpcion,TVariablesPrograma.GetInstance.TipoDoc_Fp1)
    else if not(FrmAuxBasico is TFrmAuxFP01) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP01.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'');
    end;
  end
  else if AAccion = 'UbicMercan' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self, AOpcion,'')
    else if not(FrmAuxBasico is TFrmAuxFP02) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'');

    end;
  end
   else if AAccion = 'ENT_UbicMercan' then
  begin
    if not assigned(FrmAuxBasico) then
    begin
      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self, AOpcion,'Entradas');
      end
    else
    begin

      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'Entradas');

    end;
    end
    else if AAccion = 'INT_UbicMercan' then
  begin
        if not assigned(FrmAuxBasico) then
        begin

      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self, AOpcion,'Internas');
      end
    else
    begin
       FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'Internas');
    end;
  end
  else if AAccion = 'REG_UbicMercan' then
  begin
     if not assigned(FrmAuxBasico) then
       FrmAuxBasico.Free;

      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'Regulariza');
  end
  else if AAccion = 'ENC_UbicMercan' then
  begin
     if not assigned(FrmAuxBasico) then
       FrmAuxBasico.Free;

      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'ENC');
  end
    else if AAccion = 'NPR_UbicMercan' then
  begin
     if not assigned(FrmAuxBasico) then
       FrmAuxBasico.Free;

      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'NPR');
  end
  else if AAccion = 'INN_UbicMercan' then
  begin
     if not assigned(FrmAuxBasico) then
       FrmAuxBasico.Free;

      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'INN');
  end
    else if AAccion = 'OUT_UbicMercan' then
  begin
     if not assigned(FrmAuxBasico) then
       FrmAuxBasico.Free;

      FrmAuxBasico := TFrmAuxFP02.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'OUT');
  end
  else if AAccion = 'PrepaPed' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP3.Create(Self, DataMBasico.Sesion, Self, AOpcion,'')
    else if not(FrmAuxBasico is TFrmAuxFP3) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP3.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'');
    end;
  end
   else if AAccion = 'PrepaPed_EXP' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP3.Create(Self, DataMBasico.Sesion, Self, AOpcion,'EXPEDIR')
    else if not(FrmAuxBasico is TFrmAuxFP3) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP3.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'EXPEDIR');
    end;
  end
  
    else if AAccion = 'GestInv' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP05.Create(Self, DataMBasico.Sesion, Self, AOpcion)
    else if not(FrmAuxBasico is TFrmAuxFP05) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP05.Create(Self, DataMBasico.Sesion, Self,
        AOpcion);
    end;
  end
    else if AAccion = 'Crossdoc' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP06.Create(Self, DataMBasico.Sesion, Self, AOpcion)
    else if not(FrmAuxBasico is TFrmAuxFP06) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP06.Create(Self, DataMBasico.Sesion, Self,
        AOpcion);
    end;
  end
   else if AAccion = 'Expedicion' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP07.Create  (Self, DataMBasico.Sesion, Self, AOpcion)
    else if not(FrmAuxBasico is TFrmAuxFP07) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP07.Create(Self, DataMBasico.Sesion, Self,
        AOpcion);
    end;
  end
     else if AAccion = 'Producc' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP08.Create(Self, DataMBasico.Sesion, Self, AOpcion)
    else if not(FrmAuxBasico is TFrmAuxFP08) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP08.Create(Self, DataMBasico.Sesion, Self,
        AOpcion);
    end;
  end
  else if AAccion = 'Train' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP09.Create(Self, DataMBasico.Sesion, Self, AOpcion)
    else if not(FrmAuxBasico is TFrmAuxFP09) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP09.Create(Self, DataMBasico.Sesion, Self,
        AOpcion);
    end;
  end
  else if AAccion = 'Despacho' then
  begin
    if not assigned(FrmAuxBasico) then
      FrmAuxBasico := TFrmAuxFP31.Create(Self, DataMBasico.Sesion, Self, AOpcion)
    else if not(FrmAuxBasico is TFrmAuxFP31) then
    begin
      FrmAuxBasico.Free;
      FrmAuxBasico := TFrmAuxFP31.Create(Self, DataMBasico.Sesion, Self,
        AOpcion);
    end;
  end

    else if AAccion = 'CargaCamiones' then
  begin
     if not assigned(FrmAuxBasico) then
       FrmAuxBasico.Free;

     FrmAuxBasico := TFrmAuxFP90.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'');
  end
  else if AAccion = 'AuxMapaAlmacen' then
  begin
    if not assigned(FrmAuxBasico) then
       FrmAuxBasico.Free;

     FrmAuxBasico := TFrmAuxFP97.Create(Self, DataMBasico.Sesion, Self,
        AOpcion,'');
  end;

  if //(tvariablesPrograma.GetInstance.NOMBRE_ALMACEN='ENCE')
  //or
  (tvariablesPrograma.GetInstance.NOMBRE_ALMACEN='MARISMAS')

  then //SON LOS  UNICOS QUE USA WSERVER CON MAPEO PUERTOS
  begin
           if trystrtofloat(getusername,vdoble) then //averiguo si es un terminal
         begin
              try
              ShellExecute(0,'open',PWideChar('mapeopu.exe'),nil,pwidechar(extractfilepath(application.ExeName)),SW_MINIMIZE);
              //showmessage('aaa');
              except
                //
              end;
         end;
  end;
 //COMENTADO 30/11/2020
 {
  if trystrtofloat(getusername,vdoble) then //averiguo si es un terminal
   begin
        try
        ShellExecute(0,'open',PWideChar('mapeopu.exe'),nil,pwidechar(extractfilepath(application.ExeName)),SW_MINIMIZE);
        except
          //
        end;
   end;


   if trystrtofloat(getusername,vdoble) then //averiguo si es un terminal
   begin
    try
      ShellExecute(0,'open',PWideChar('ShutdownAuto.exe'),nil,pwidechar(extractfilepath(application.ExeName)),SW_MINIMIZE);
    except
      //
    end;

   end;
  FINDE COMENTADO 30/11/2020
}

  FrmAuxBasico.Parent := Self.PanelCliente;
  FrmAuxBasico.Color:=clblack;
  FrmAuxBasico.Align := alClient;
  FrmAuxBasico.Show;
   //if TVariablesPrograma.GetInstance.HeightNuevo <=strtoint(TVariablesPrograma.GetInstance.resolucion) then
     //FrmAuxBasico.ScaleBy(TVariablesPrograma.GetInstance.HeightNuevo, TAMBASEFORM)
      //
  // end
  // else
  // EscribirMensajeAyuda
  // (Format('El operario %s no tiene permiso para esta opción de menú',
  // [TVariablesPrograma.GetInstance.Usuario]), '00');
end;

procedure TFormPrincipal.ActivaControles;
begin
  EditInput.Enabled := True;
  BtnMenu.Enabled := True;
  BtnInicio.Visible := not FrmAuxLogon.Visible;
  BtnInicio.Caption := 'INICIO';
end;

procedure TFormPrincipal.ActivarEditInput(Estado: TEstadosEdit);
begin
  case Estado of
    TEE_NORMAL:
      begin
        EditInput.NumbersOnly := False;
        EditInput.PasswordChar := #0;
      end;
    TEE_NUMERICO:
      begin
        // EditInput.NumbersOnly := True;
        EditInput.NumbersOnly := False;
        EditInput.PasswordChar := #0;
      end;
    TEE_PASSWORD:
      begin
        EditInput.NumbersOnly := False;
        EditInput.PasswordChar := '*';
      end;
  end; 
  try editinput.setfocus;  except  end;//OJOOJO
end;

procedure TFormPrincipal.AlmacenaMensajeFlujo(AMensaje, AOpcMenu: String);
begin
  FMensajeFlujo := AMensaje;
  FOpcMenu := AOpcMenu;
end;

procedure TFormPrincipal.BloqueaControles;
begin
  EditInput.Enabled := False;
  BtnMenu.Enabled := False;
  BtnInicio.Visible := True;
  BtnInicio.Caption := 'CONTINUAR';
end;

procedure TFormPrincipal.BtnF1Click(Sender: TObject);
begin
  inherited;
    editinput.Text:=editinput.text+TVariablesPrograma.GetInstance.BtnF1;
    try  editinput.SetFocus; except {} end;
    editinput.SelStart:=length( editinput.text)+1;
end;

procedure TFormPrincipal.BtnF2Click(Sender: TObject);
begin
  inherited;
    editinput.Text:=editinput.text+TVariablesPrograma.GetInstance.BtnF2;
    try  editinput.SetFocus; except {} end;
    editinput.SelStart:=length( editinput.text)+1;
end;

procedure TFormPrincipal.BtnF3Click(Sender: TObject);
begin
  inherited;
    editinput.Text:=editinput.text+TVariablesPrograma.GetInstance.BtnF3;
    try  editinput.SetFocus; except {} end;
    editinput.SelStart:=length( editinput.text)+1;
end;

procedure TFormPrincipal.BtnF4Click(Sender: TObject);
begin
  inherited;
    editinput.Text:=editinput.text+TVariablesPrograma.GetInstance.BtnF4;
     try  editinput.SetFocus; except {} end;
    editinput.SelStart:=length( editinput.text)+1;
end;

procedure TFormPrincipal.BTNf5Click(Sender: TObject);
begin
  inherited;
    editinput.Text:=editinput.text+TVariablesPrograma.GetInstance.BtnF5;
     try  editinput.SetFocus; except {} end;
    editinput.SelStart:=length( editinput.text)+1;
end;

procedure TFormPrincipal.BtnF6Click(Sender: TObject);
begin
  inherited;
   inherited;
    editinput.Text:=editinput.text+TVariablesPrograma.GetInstance.BtnF6;
     try  editinput.SetFocus; except {} end;
    editinput.SelStart:=length( editinput.text)+1;
end;


procedure TFormPrincipal.BtnInicioClick(Sender: TObject);
begin
  inherited;
  if (UpperCase(BtnInicio.Caption) = 'INICIO') then
    FrmAuxBasico.IniciaForm ('BTN')
  else if (UpperCase(BtnInicio.Caption) = 'ATRAS') then
    FrmAuxBasico.AtrasForm

  else if (UpperCase(BtnInicio.Caption) = 'VOLVER') then
    FrmAuxBasico.DevolverEstadoBoton
  else if (UpperCase(BtnInicio.Caption) = 'CONTINUAR') then
  begin
    if FMensajeFlujo <> '' then
    begin
      EscribirMensajeAyuda(FMensajeFlujo, FOpcMenu);
      FMensajeFlujo := '';
    end;
    if assigned(FrmAuxBasico) then
      if FrmAuxBasico.Visible then
      begin
        FMensajeFlujo := '' ;             //CADSDS
       // FrmAuxBasico.DevolverEstadoBoton;
      end
      else if assigned(FrmAuxLogon) then
        if FrmAuxLogon.Visible then
          FrmAuxLogon.DevolverEstadoBoton;
  end;
   ActivaControles;
  if EditInput.CanFocus then
    EditInput.SetFocus;

  if TVariablesPrograma.GetInstance.CambiarEstado='CAMBIAR' then
  begin

     // REINICIO LAS VARIABLES

     TVariablesPrograma.GetInstance.CambiarEstado := '';

     FormPrincipal.BtnF1.Caption:= TVariablesPrograma.GetInstance.BtnF1;
     FormPrincipal.BtnF2.Caption:= TVariablesPrograma.GetInstance.BtnF2;
     FormPrincipal.BtnF3.Caption:= TVariablesPrograma.GetInstance.BtnF3;
     FormPrincipal.BtnF4.Caption:= TVariablesPrograma.GetInstance.BtnF4;
     FormPrincipal.BtnF5.Caption:= TVariablesPrograma.GetInstance.BtnF5;
     FormPrincipal.BtnF6.Caption:= TVariablesPrograma.GetInstance.BtnF6;

  end;

end;

procedure TFormPrincipal.BtnMenuClick(Sender: TObject);
begin
  inherited;
  if assigned(FrmAuxBasico) then
    FrmAuxBasico.Visible := False;
  if assigned(FrmAuxLogon) then
    FrmAuxLogon.Visible := True;
  BtnInicio.Visible := False;
  if EditInput.CanFocus then
  begin
    limpiarEditInput;
    EditInput.SetFocus;
  end;

    if TVariablesPrograma.GetInstance.CambiarEstado='CAMBIAR' then
  begin

     // REINICIO LAS VARIABLES

     TVariablesPrograma.GetInstance.CambiarEstado := '';

     FormPrincipal.BtnF1.Caption:= TVariablesPrograma.GetInstance.BtnF1;
     FormPrincipal.BtnF2.Caption:= TVariablesPrograma.GetInstance.BtnF2;
     FormPrincipal.BtnF3.Caption:= TVariablesPrograma.GetInstance.BtnF3;
     FormPrincipal.BtnF4.Caption:= TVariablesPrograma.GetInstance.BtnF4;
     FormPrincipal.BtnF5.Caption:= TVariablesPrograma.GetInstance.BtnF5;
     FormPrincipal.BtnF6.Caption:= TVariablesPrograma.GetInstance.BtnF6;

  end;

end;

procedure TFormPrincipal.BtnResolucionClick(Sender: TObject);
begin
  inherited;
  if assigned(FrmAuxBasico) then
    FrmAuxBasico.MostrarAyuda;
  if EditInput.CanFocus then
    EditInput.SetFocus;
  {
    Ancho := StrToInt(Copy(BtnResolucion.Caption, 0, 3));
    CambiaResolucion(Ancho, TVariablesPrograma.GetInstance.HeightNuevo);
    if EditInput.CanFocus then
    EditInput.SetFocus;
  }
end;

procedure TFormPrincipal.BtnSalirClick(Sender: TObject);
begin
  ActivarEditInput(TEE_NORMAL);
  limpiarEditInput;
  if (UpperCase(BtnMenu.Caption) = 'INICIO') then
  begin
    TVariablesPrograma.GetInstance.Usuario := '';
    if assigned(FrmAuxBasico) and FrmAuxBasico.Visible then
      FrmAuxBasico.IniciaForm('BTN')
    else if (assigned(FrmAuxLogon) and FrmAuxLogon.Visible) then
      FrmAuxLogon.IniciaForm('BTN');
    EditInput.SetFocus;
  end
  else if (BtnMenu.Caption = 'Salir') then
    CerrarAplicacion
  else if (BtnMenu.Caption = 'MENÚ') then
    EditInput.SetFocus;
end;



procedure TFormPrincipal.CambiarCaptionBotonInicio(ACaption: String);
begin
  if assigned(FrmAuxLogon) then
  begin
    if FrmAuxLogon.Visible then
      BtnMenu.Caption := ACaption;
  end;
  if (assigned(FrmAuxBasico)) then
    if FrmAuxBasico.Visible then
      BtnInicio.Caption := ACaption;

end;

procedure TFormPrincipal.CambiaResolucion(HeightNuevo, HeightActual: Integer);
begin
 { case HeightNuevo of
    320:
      BtnResolucion.Caption := '640x480';
    640:
      BtnResolucion.Caption := '800x600';
    800:
      BtnResolucion.Caption := '320x240';
  end;
  }
      ScaleBy(strtoint(TVariablesPrograma.GetInstance.Resolucion),HeightActual);
  // 20:31 1405 ScaleBy(HeightNuevo, HeightActual);
  TVariablesPrograma.GetInstance.CambiaResolucion(HeightNuevo, HeightActual);
end;

procedure TFormPrincipal.CerrarAplicacion;
begin
  if TVariablesPrograma.GetInstance.CierraTerminal = 'S' then
    ShutDownWindows(EWX_LOGOFF);

  Application.Terminate;
end;



function TFormPrincipal.CompruebaAlmacenInput(AInput: String;
  AOpcMenu: String): Boolean;
begin
  if StrToInt(Copy(AInput, 1, 4)) <>
    StrToInt(TVariablesPrograma.GetInstance.CodAlm) then
  begin
    Result := True;
    BloqueaControles;
    EscribirMensajeAyuda(RS_ErrorCodAlm, AOpcMenu);
  end
  else
    Result := True;
end;

procedure TFormPrincipal.CreaFormAuxiliar;
var
SR: TSearchRec;
   IsFound:boolean;
   DiferenciaMinutos, diferenciaProceso,DifSegundos:real;
   horaantes:Tdatetime;
   filecopia:string;
begin
  if not assigned(FrmAuxLogon) then
  begin
    FrmAuxLogon := TFrmAuxLogin.Create(Self, DataMBasico.Sesion, Self);
    FrmAuxLogon.Parent := Self.PanelCliente;
    FrmAuxLogon.Align := alClient;

//  2020 140512014  FrmAuxLogon.ScaleBy(TVariablesPrograma.GetInstance.HeightNuevo,

       FrmAuxLogon.ScaleBy(strtoint(TVariablesPrograma.GetInstance.Resolucion) ,
      TAMBASEFORM);

      //BORRO LOS ANTIGUOS

  IsFound := FindFirst('salida*.bat', faAnyFile - faDirectory, SR) = 0;
    // MIentras encuentre
    while IsFound do  begin
        filecopia:=SR.name;
          DiferenciaMinutos:=MinuteSpan(GetFilemODIFYTime(filecopia),now);
            if DiferenciaMinutos>10 then
            begin
              deletefile(filecopia);
            end;
      IsFound := FindNext(SR) = 0;
    end;
  end;
end;

procedure TFormPrincipal.EditInputChange(Sender: TObject);
var
  formControl: TFormAuxBasico;
  existesufijo:Boolean;
  Input:String;
  SufijoPuntoPunto,cadenadeceros:String;
  longitudSufijoPuntoPunto:integer;

begin
  inherited;

     if pos('{',EditInput.Text) >0  then
     begin

      EditInput.Text:=Stringreplace(Editinput.text,'{' ,'*0791', [rfReplaceAll, rfIgnoreCase]) ;
      EditInputKeyWearable('INTRO');
     end;

		 if pos('*0791',EditInput.Text) >0  then
       begin
           //LLAMO A LAS BASCULAS
          DameBasculaTCPIP('1');
          exit;
       end;
     if pos('*0792',EditInput.Text) >0  then
       begin
           //LLAMO A LAS BASCULAS
          DameBasculaTCPIP('2');
          exit;
       end;
     if pos('*0793',EditInput.Text) >0  then
       begin
           //LLAMO A LAS BASCULAS
          DameBasculaTCPIP('3');
          exit;
       end;
     if pos('*0794',EditInput.Text) >0  then
       begin
           //LLAMO A LAS BASCULAS
          DameBasculaTCPIP('4');
          exit;
       end;
     if pos('*0795',EditInput.Text) >0  then
       begin
           //LLAMO A LAS BASCULAS
          DameBasculaTCPIP('5');
          exit;
       end;
   existesufijo:=false;
   if TVariablesPrograma.GetInstance.Sufijo='' then
     begin
       existesufijo:=True;    //Si las pistolas no llevan Sufijo esta variable es siempre true
       Input:=editinput.text;
     end
   else
     begin
     if POS(TVariablesPrograma.GetInstance.Sufijo,EditInput.Text)>0 then
       begin
         existesufijo:=True;
         input :=StringReplace(EditInput.Text,
                  ' '+TVariablesPrograma.GetInstance.Sufijo,
                  '',
                 [rfReplaceAll, rfIgnoreCase]);
       end;
   end;



  if FrmAuxLogon.Visible then
    formControl := FrmAuxLogon
  else
    formControl := FrmAuxBasico;

      if (EditInput.text='=') then
        begin
         existesufijo:=True;
         Input:='=';
        end;

      if (EditInput.text=TvariablesPrograma.GetInstance.TeclaRefresca_FP1 )
        or  (EditInput.text=TvariablesPrograma.GetInstance.TeclaValidaUbicacion )
         then
         begin
           existesufijo:=True;
           Input:=EditInput.text;
         end;

      if TvariablesPrograma.GetInstance.CambiarEstado = 'CAMBIAR' then
         begin
          existesufijo:=True;

            // CAMBIO LOS VALORES QUE TIENEN LOS BOTONES POR LOS ESTADOS
            // QUE VAN ASOCIADOS A CADA UNO DE ELLOS
            if EditInput.Text = TVariablesPrograma.GetInstance.BtnF2 then
            Input:='P';
            if EditInput.Text = TVariablesPrograma.GetInstance.BtnF3 then
            Input:='A';
            if EditInput.Text = TVariablesPrograma.GetInstance.BtnF4 then
            Input:='R';

         end;


      if (formControl.ControlInput(Input)) OR (existesufijo) then
      begin

        if (input <> '')  then
        begin
         //vamos a automatizar Grabar MAtriculas o expediciones

            //      memoayuda.Lines.Insert(0,'entro');
    //      if formControl.ValidaInput(EditInput.Text) then
                 if  (copy (Input,1,6) ='270000') and  (length(input)=13) then
             begin

                 //Esto es para Admitir Matriculas de Friologic
                 Input:='91..'+Input;//StringReplace(EditInput.Text,'..',cadenadeceros,[rfReplaceAll, rfIgnoreCase]);
             end;

              if  (copy (Input,1,7) ='3270000') and  (length(input)=14) then
             begin
                 //Esto es para Admitir Matriculas de Friologic
                 Input:='91..'+copy(Input,2,15);//StringReplace(EditInput.Text,'..',cadenadeceros,[rfReplaceAll, rfIgnoreCase]);
             end;

               if  (copy (EditInput.Text,1,7) ='3220000') and  (length(editinput.Text)=14) then
             begin
                 //Esto es para Admitir Matriculas de Friologic antiguas
                 EditInput.Text:=copy(EditInput.Text,2,13);//StringReplace(EditInput.Text,'..',cadenadeceros,[rfReplaceAll, rfIgnoreCase]);
             end;

              if  (copy (EditInput.Text,1,7) ='3910000') and  (length(editinput.Text)=19) then
             begin
                 //Esto es para Admitir Matriculas de Friologic antiguas
                 EditInput.Text:=copy(EditInput.Text,2,18);//StringReplace(EditInput.Text,'..',cadenadeceros,[rfReplaceAll, rfIgnoreCase]);
             end;


              //Metodo abreviado introducir matriculas
           if (copy (input,1,4) ='91..') and  (length(input)>4) then
             begin
                 SufijoPuntoPunto:= copy (Input,5,100);
                 longitudSufijoPuntoPunto:=length(SufijoPuntoPunto);
                 //cadenadeceros:=Format ('%.*d', [16, strtoint(SufijoPuntoPunto)]);  // S = '0000000123'
                  cadenadeceros:=RellenaCAdena(SufijoPuntoPunto,16,'0','I');

                 Input:='91'+cadenadeceros;//StringReplace(EditInput.Text,'..',cadenadeceros,[rfReplaceAll, rfIgnoreCase]);

           end;
           if (copy (input,1,7) ='91101..') and  (length(input)>4) then
             begin
                 SufijoPuntoPunto:= copy (Input,8,100);
                 longitudSufijoPuntoPunto:=length(SufijoPuntoPunto);
                 //cadenadeceros:=Format ('%.*d', [16, strtoint(SufijoPuntoPunto)]);  // S = '0000000123'
                  cadenadeceros:=RellenaCAdena(SufijoPuntoPunto,13,'0','I');

                 Input:='91101'+cadenadeceros;//StringReplace(EditInput.Text,'..',cadenadeceros,[rfReplaceAll, rfIgnoreCase]);

           end;


         if formControl.ValidaInput(input)  then
                ActivaControles;
//          else
  //            BloqueaControles;//COmentado LAser CGAlica 02/10/2022

          EditInput.SelectAll;
          if EditInput.CanFocus then
            EditInput.SetFocus;
        end;
      end;

end;

procedure TFormPrincipal.EditInputKeyPress(Sender: TObject; var Key: Char);
begin
  inherited;

  //showmessage(key);
  if key='(' then key:='#';
  if key='·' then key:='#';
  if key='ç' then key:='+';
  if key='Ç' then key:='+';
  //if key='j' then key:=#8; NO SE PA QUIEN ES PERO PARA GYEMO LO DEBO comentar
  if key='}' then key:='+';
  if key='[' then key:='#';
 if key='ˋ' then key:='#';
 if key='!' then key:='-';

   if TVariablesPrograma.GetInstance.NOMBRE_ALMACEN='PROGANDO' then
   //
   else
   begin
      if key='/' then key:='#';///FRUVERPACK 24052022
   end;

  if TVariablesPrograma.GetInstance.LEER_GS1 = 'N' then
  begin
      if key=']' then key:='_';
  end;

  if TVariablesPrograma.GetInstance.ValorTeclaPunto=',' then
  begin
    if key='.' then key:=',';
  end ;


       if (ord(Key) = VK_RETURN)  then
      EditInputKeyWearable('INTRO');

 { if (ord(Key) = VK_RETURN) and (EditInput.text='') and
  (
    (AAccionActiva = 'ENT_UbicMercan') or (AAccionActiva = 'INT_UbicMercan')
  )
   then    ///Para que la opcion 02 se recarge solo co INTRO
  begin
      EditInput.Text:='0';
  end ;
  }
end;

procedure TFormPrincipal.EditInputKeyUp(Sender: TObject; var Key: Word;
  Shift: TShiftState);
var
  formControl: TFormAuxBasico;
  i:integer;
  SufijoPuntoPunto,cadenadeceros:String;
  longitudSufijoPuntoPunto:integer;
begin
  inherited;
  if FrmAuxLogon.Visible then
    formControl := FrmAuxLogon
  else
    formControl := FrmAuxBasico;

  if (Key = VK_RETURN)  then
      EditInputKeyWearable('INTRO')
  else
    formControl.EjecutaTecla(Key, EditInput.Text);

end;

procedure TFormPrincipal.EditInputKeyWearable(Tecla:String);
var
  formControl: TFormAuxBasico;
  i:integer;
  SufijoPuntoPunto,cadenadeceros:String;
  longitudSufijoPuntoPunto:integer;
begin
  inherited;

  if FrmAuxLogon.Visible then
    formControl := FrmAuxLogon
  else
    formControl := FrmAuxBasico;

  /// if ((Key = VK_RETURN) or (formControl.ControlInput(EditInput.Text)))
  if (Tecla = 'INTRO')
  /// and FrmAuxLogon.Visible))
  /// ///Volvemos a activar el control para todos
  then
  begin
    if EditInput.Text <> '' then
    begin
          //Metodo abreviado introducir matriculas

         if  (copy (EditInput.Text,1,6) ='270000') and  (length(editinput.Text)=13) then
         begin
             //Esto es para Admitir Matriculas de Friologic antiguas
             EditInput.Text:='91..'+EditInput.Text;//StringReplace(EditInput.Text,'..',cadenadeceros,[rfReplaceAll, rfIgnoreCase]);
         end;

         if  (copy (EditInput.Text,1,7) ='3270000') and  (length(EditInput.Text)=14) then
         begin
             //Esto es para Admitir Matriculas de Friologic
            EditInput.Text:='91..'+copy(EditInput.Text,2,15);//StringReplace(EditInput.Text,'..',cadenadeceros,[rfReplaceAll, rfIgnoreCase]);
         end;

       if  (copy (EditInput.Text,1,4) ='91..') and  (length(editinput.Text)>4) then
         begin
             SufijoPuntoPunto:= copy (EditInput.Text,5,100);
             longitudSufijoPuntoPunto:=length(SufijoPuntoPunto);
            // cadenadeceros:=Format ('%.*d', [16, strtoint(SufijoPuntoPunto)]);  // S = '0000000123'
              cadenadeceros:=RellenaCAdena(SufijoPuntoPunto,16,'0','I');

             EditInput.Text:='91'+cadenadeceros;//StringReplace(EditInput.Text,'..',cadenadeceros,[rfReplaceAll, rfIgnoreCase]);
         end;

      if  (copy (EditInput.Text,1,7) ='91101..') and  (length(editinput.Text)>4) then
         begin
             SufijoPuntoPunto:= copy (EditInput.Text,8,100);
             longitudSufijoPuntoPunto:=length(SufijoPuntoPunto);
            // cadenadeceros:=Format ('%.*d', [16, strtoint(SufijoPuntoPunto)]);  // S = '0000000123'
              cadenadeceros:=RellenaCAdena(SufijoPuntoPunto,13,'0','I');

             EditInput.Text:='91101'+cadenadeceros;//StringReplace(EditInput.Text,'..',cadenadeceros,[rfReplaceAll, rfIgnoreCase]);
         end;
       if pos('*0791',EditInput.Text) >0  then
       begin
          DameBasculaTCPIP('1');
          exit;
       end;

         if pos('*0792',EditInput.Text) >0  then
       begin
           //LLAMO A LAS BASCULAS
          DameBasculaTCPIP('2');
          exit;
       end;

       if pos('*0793',EditInput.Text) >0  then
       begin
           //LLAMO A LAS BASCULAS
          DameBasculaTCPIP('3');
          exit;
       end;
       if pos('*0794',EditInput.Text) >0  then
       begin
           //LLAMO A LAS BASCULAS
          DameBasculaTCPIP('4');
          exit;
       end;

       if pos('*0795',EditInput.Text) >0  then
       begin
           //LLAMO A LAS BASCULAS
          DameBasculaTCPIP('5');
          exit;
       end;
      if formControl.ValidaInput(EditInput.Text) then
        ActivaControles;
     // else
       // BloqueaControles;

        EditInput.SelectAll;

      if EditInput.CanFocus then
        EditInput.SetFocus;


      if assigned(fintfp01) then
      begin

        FintFP01.PonerFocusenFecha;
        end;

    end
    else
    begin
      // BloqueaControles;
      // EscribirMensajeAyuda(RS_InputVacio, '');
    end;
  end;
end;

procedure TFormPrincipal.EscribeInput(AValor: String);
begin
  EditInput.Text := AValor;
end;

procedure TFormPrincipal.EscribirMensajeAyuda(AMensaje, AMenu: String);
begin
  // Escribe un mensaje en el memo de ayuda
  if AMensaje <> '' then
    with Self.MemoAyuda do
    begin
      Lines.Insert(0, 'FP' + AMenu + '->' + AMensaje);
      SelStart := 0;
      Perform(EM_SCROLLCARET, 0, 0);
    end;
end;

procedure TFormPrincipal.Escribirmemolog(AMensaje:String);
begin
  EscribirMensajeAyuda('',Amensaje);
end;

procedure TFormPrincipal.focoEditInput;
begin
  if EditInput.CanFocus then
    EditInput.SetFocus;
end;

function TFormPrincipal.ShutDownWindows(Flag: Word): Boolean;
// Función que permite apagar la computadora
var
  TokenPriv: TTokenPrivileges;
  H: DWord;
  HToken: THandle;
begin
  if Win32Platform = VER_PLATFORM_WIN32_NT then
  begin
    OpenProcessToken(GetCurrentProcess, TOKEN_ADJUST_PRIVILEGES, HToken);
    LookUpPrivilegeValue(NIL, 'SeShutdownPrivilege',
      TokenPriv.Privileges[0].Luid);
    TokenPriv.PrivilegeCount := 1;
    TokenPriv.Privileges[0].Attributes := SE_PRIVILEGE_ENABLED;
    H := 0;
    AdjustTokenPrivileges(HToken, False, TokenPriv, 0,
      PTokenPrivileges(NIL)^, H);
    CloseHandle(HToken);
  end;
  Result := ExitWindowsEx(Flag, 0);
end;

procedure TFormPrincipal.VolverMenu;
begin
  BtnMenu.OnClick(Self);
end;
procedure  TFormPrincipal.Split(Delimiter: Char; Str: string; ListOfStrings: TStrings) ;
begin
   ListOfStrings.Clear;
   ListOfStrings.Delimiter     := Delimiter;
   ListOfStrings.DelimitedText := Str;
end;


function TFormPrincipal.separaCadenas (CadenaOrigen:string;  SalidaList: TStringList):String;
var
  p:integer;
begin
        //A la linea le añado '|' al principio si no la trae
        if (COPY(CadenaOrigen,1,1)<>'|') AND (COPY(CadenaOrigen,1,1)<>'' )then
            CadenaOrigen:='|'+CadenaOrigen;

        CadenaOrigen:=Stringreplace(CadenaOrigen, ' ', '$#',[rfReplaceAll, rfIgnoreCase]);
        Split('|', CadenaOrigen, SalidaList);

         //Vuelvo a Poner los espacios
         for p := 0 to SalidaList.Count-1 do
         begin
           SalidaList[p]:=Stringreplace(SalidaList[p], '$#', ' ',[rfReplaceAll, rfIgnoreCase]);
         end;
        CadenaOrigen:=Stringreplace(CadenaOrigen, '$#', ' ',[rfReplaceAll, rfIgnoreCase]);
  result:=CadenaOrigen;
end;

procedure   TFormPrincipal.CreaColumnasGrid (CRdbGrid:TCRdbGrid;Busqueda:String);
var
 i :integer;
 salidaList:TstringList;
 FicheroGrid:TstringList;
 ParametroBusqueda:String;
begin
 if Busqueda='' then ParametroBusqueda:=CRdbGrid.Name
 else
 ParametroBusqueda:=Busqueda;
 

 FicheroGrid:= TStringList.Create;
try
   with FicheroGrid do
   BEGIN
      LoadFromFile(TVariablesPrograma.GetInstance.INIgridColumnas);
      SalidaList := TStringList.Create;

      i := 0;
      while i < FicheroGrid.count do
      begin
        SalidaList.Clear;
        if FicheroGrid.Strings[i] <>'' then
        begin
          FicheroGrid.Strings[i]:=separaCadenas(FicheroGrid.Strings[i],SalidaList);
            IF (copy(SalidaList[1],1,1) <>';') and (SalidaList[1]=ParametroBusqueda) THEN
               BEGIN
                  CRdbGrid.Columns.Add;
                  CRdbGrid.Columns[CRdbGrid.Columns.Count-1].FieldName:=SalidaList[2];
                  CRdbGrid.Columns[CRdbGrid.Columns.Count-1].title.caption := SalidaList[3];
                  if SalidaList[4]='taCenter' then
                     CRdbGrid.Columns[CRdbGrid.Columns.Count-1].Alignment :=taCenter
                     else
                  if SalidaList[4]='taLeft' then
                     CRdbGrid.Columns[CRdbGrid.Columns.Count-1].Alignment :=taLeftJustify
                     else
                  if SalidaList[4]='taRigth' then
                     CRdbGrid.Columns[CRdbGrid.Columns.Count-1].Alignment :=taRightJustify;

                  CRdbGrid.Columns[CRdbGrid.Columns.Count-1].Width     := strtoint(SalidaList[5]);
                  CRdbGrid.Columns[CRdbGrid.Columns.Count-1].Title.Alignment := taCenter;
				CRdbGrid.Columns[CRdbGrid.Columns.Count-1].Title.font.Size:=strtoint(TVariablesPrograma.GetInstance.FontMensajes);																													
                end;

          end;

         INC(i);
      end;
   END;
   finally
     freeandnil(fICHEROgRID);
      freeandnil(SalidaList);
    end;
end;

function TFormPrincipal.NumcaracteresAlfabeticos (CadenaOrigen:string):Integer;
  const
    VALIDOS=['A'..'Z','a'..'z'];//,'a'..'z','á','é','í','ó','ú','(',')','+','-','*','/','<','>','=','!','~','^',';','.',',','‘','@','%','“','#','$','&','_','|','{','}','?','[',']' ];
   var
  p,i:integer;
  cadenatratada:String;
begin
p:=0;
        //Elimino los carateres no validos en PL/SQL
        for i:= 1 to Length(upperCase(CadenaOrigen)) do
           if CadenaOrigen[i] in VALIDOS then p:=p+1;
        result:=p;
end;


Procedure TFormPrincipal.GrabaOrdenImpresionOperario (Impresora:string);
 var
 RutaArchivo,NombreArchivo,fechaact,fechahora,PrefijoOperario:String;
  fichero:TextFile;
begin
     RutaArchivo:=TvariablesPrograma.GetInstance.RutaImpresionDesatendida;
     fechaact:=datetostr(date)+' '+timetostr(time);
     fechahora :=formatdatetime('ddmmyyhhmmss',strtodatetime(FECHAACT));
     PrefijoOperario:= inttostr(TvariablesPrograma.GetInstance.IdOperario)+'_';
     //showmessage(TvariablesPrograma.GetInstance.I0772);
     NombreArchivo:=rutaArchivo+'\'+PrefijoOperario+'_'+fechahora+'OPE.JOB';
     AssignFile(fichero,NombreArchivo);
     rewrite(fichero);
     if Impresora='0771' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0771);
     if Impresora='0772' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0772);
     if Impresora='0773' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0773);
     if Impresora='0774' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0774);
     if Impresora='0775' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0775);
     if Impresora='0776' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0776);
     if Impresora='0777' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0777);
     if Impresora='0778' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0778);
     if Impresora='0779' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0779);
     if Impresora='0780' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0780);
     if Impresora='0781' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0781);
     if Impresora='0782' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0782);
     if Impresora='0783' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0783);
     if Impresora='0784' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0784);
     if Impresora='0785' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0785);
     if Impresora='0786' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0786);
     if Impresora='0787' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0787);
     if Impresora='0788' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0788);
     if Impresora='0789' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0789);

     if Impresora='0790' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0790);
     if Impresora='0791' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0791);
     if Impresora='0792' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0792);
     if Impresora='0793' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0793);
     if Impresora='0794' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0794);
     if Impresora='0795' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0795);
     if Impresora='0796' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0796);
     if Impresora='0797' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0797);
     if Impresora='0798' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0798);
     if Impresora='0799' then
           writeln(fichero, TvariablesPrograma.GetInstance.I0799);






     closeFILE(fichero);
end;



function  TFormPrincipal.IMPRIMETRABAJO(STringLISTFCIHEROS:TStringList;OPERARIO, NOMBREFICHERO,TipoPapel,vImpresora,numeroetiquetas: string): string;

var
  salida,nombresalidabat,filecopia: string;
  salidalog,salidaBAT: TextFile;
  puerto, i: Integer;
   DiferenciaMinutos:real;
    diferenciaProceso,DifSegundos:real;
   horaantes:Tdatetime;
begin
    if NOMBREFICHERO='' then
    begin
        if STringLISTFCIHEROS.Count>0 then
        begin
          nombresalidabat:=  extractfilepath(application.ExeName)+'salidaLPT'+tvariablesprograma.GetInstance.DamefechaHOrafichero+OPERARIO+'.bat';
          AssignFile(salidaBAT,nombresalidabat);
          rewrite(salidaBAT);
          i:=0;
                      while i < STringLISTFCIHEROS.Count do
                     begin

                       if (vimpresora='BT1')
                        or (vimpresora='BT2') then
                        begin
                            salida :='copy '+STringLISTFCIHEROS[i]  +' '+ pchar(IncludeTrailingPathDelimiter(TvariablesPrograma.GetInstance.RutaServicioAAInterface)+vimpresora);
                       end
                       else
                       begin
                         salida := 'type ' + ' ' + STringLISTFCIHEROS[i] + ' ' + '>' + vimpresora;
                       end;
                         writeln(salidaBAT, salida);

                       Inc(i);
                     end;

          closefile(salidaBAT);
          salida := '';

        end;



    end
    else
    begin


           if (vimpresora='BT1')
          or (vimpresora='BT2') then
          begin
              salida :='copy '+NOMBREFICHERO +' '+ pchar(IncludeTrailingPathDelimiter(TvariablesPrograma.GetInstance.RutaServicioAAInterface)+vimpresora);
         end
         else
         begin
              salida := 'type ' + ' ' + NOMBREFICHERO + ' ' + '>' + vimpresora;
         end;
         // salida := 'type ' + ' ' + NOMBREFICHERO + ' ' + '>' + vimpresora;
          nombresalidabat:=  extractfilepath(application.ExeName)+'salidaLPT'+tvariablesprograma.GetInstance.DamefechaHOrafichero+OPERARIO+'.bat';
          AssignFile(salidaBAT,nombresalidabat);
          rewrite(salidaBAT);

          writeln(salidaBAT, salida);
          closefile(salidaBAT);
          salida := '';
   end ;

           {horaantes:=now;
             while DifSegundos<1 do
             begin
                     diferenciaProceso:=SecondSpan(horaantes,now);

                     DifSegundos:=SecondSpan(GetFilemODIFYTime(nombresalidabat),now);

                  if not FileExists(nombresalidabat) and (diferenciaProceso > 8 ) then//si en ocho segundos no sale
                       DifSegundos:=diferenciaProceso;


             end;
            }
           //  PrintTextFroMservice (Vimpresora, NOMBREFICHERO);
          //ShellExecute(0,'open',PWideChar(nombresalidabat),nil,nil,SW_MINIMIZE);



          //WinExec(PAnsiChar( 'salidaLPT'+OPERARIO+'.bat'), SW_minimize);
          if (vimpresora='LPT1')
          or (vimpresora='LPT2')
          or (vimpresora='LPT3')
          or (vimpresora='LPT4')
          or (vimpresora='LPT5')
          or (vimpresora='LPT6')
          or (vimpresora='LPT7')
          or (vimpresora='LPT8')
          or (vimpresora='LPT9')
          then

          begin
          ShellExecute(0,'open',PWideChar(nombresalidabat),nil,pwidechar(extractfilepath(application.ExeName)),SW_MINIMIZE);

         end  ///fin de   vimpresora='LPT1')    or (vimpresora='LPT2')    -..................
         else
         if (vimpresora='BT1')
          or (vimpresora='BT2') then
          begin
            //Para impresoras Bluetooh solo debo copiar el arcivo resultante en la carpeta
            //del impresora qye representa las bluetooh
            if not directoryexists(IncludeTrailingPathDelimiter(TvariablesPrograma.GetInstance.RutaServicioAAInterface)+vimpresora) then
            begin
              createdir(IncludeTrailingPathDelimiter(TvariablesPrograma.GetInstance.RutaServicioAAInterface)+vimpresora);
            end;


          ShellExecute(0,'open',PWideChar(nombresalidabat),nil,pwidechar(extractfilepath(application.ExeName)),SW_MINIMIZE);

          end  //fin de   vimpresora='BT1')  or (vimpresora='BT2')
         else
         begin
           PrintTextFroMservice (Vimpresora, NOMBREFICHERO);
         end;


end;

function  TFormPrincipal.Es_fecha(Cadena_origen:String): string;
var
 Cadena_salida,dia,mes,ano:string;
 vfecha :TDateTime;
  vAno, vMes, vDia:word;
begin
//admitimos
//  010117
//  01012017
//  31/01/17
//  31-01-17
//  31/01/2017
//  31-01-2017

   Cadena_Salida:=StringReplace(Cadena_origen, '-','/',[rfReplaceAll, rfIgnoreCase]);
   if  (Pos('/', Cadena_Salida) >0) then //   31/01/17 31-01-17
   begin
       if trystrtodate(cadena_salida,vfecha) then
       begin
           DecodeDate(vfecha, vAno, vMes, vDia);
                   Dia := FormatFloat('00',vDia);
                   Mes := FormatFloat('00',vMes);
                   Ano := Copy(IntToStr(vAno), 1, 4);

       end;

   end;

   if  (Pos('/', Cadena_Salida) =0) then
   begin
       if length(cadena_salida)=6 then   //010117
       begin
           Dia := copy(cadena_salida,1,2);
           Mes := copy(cadena_salida,3,2);
           Ano := copy(cadena_salida,5,2);
       end;

       if length(cadena_salida)=8 then   //01012017
       begin
           Dia := copy(cadena_salida,1,2);
           Mes := copy(cadena_salida,3,2);
           Ano := copy(cadena_salida,5,4);
       end;

   end;

   cadena_salida:=DIA+'/'+mes+'/'+ano;

   if trystrtodate(cadena_salida,vfecha) and  (ano >='15') and (ano <= '25') then
      result:=cadena_salida
      else
      result:='NO';


end;

function  TFormPrincipal.DameBasculaTCPIP(NumBascula:String):String;
var
  IP:String;
  PORT:integer;


begin
  PORT:=23;
  ClientSocketBascula.Active := False;

   if nUMbascula='1' then
     Ip:=TvariablesPrograma.GetInstance.BASCULA_TCPIP1;
   if nUMbascula='2' then
     Ip:=TvariablesPrograma.GetInstance.BASCULA_TCPIP2;
    if nUMbascula='3' then
         Ip:=TvariablesPrograma.GetInstance.BASCULA_TCPIP3;
    if nUMbascula='4' then
         Ip:=TvariablesPrograma.GetInstance.BASCULA_TCPIP4;
    if nUMbascula='5' then
         Ip:=TvariablesPrograma.GetInstance.BASCULA_TCPIP5;


   ClientSocketBascula.host:=IP;
   ClientSocketBascula.Port:=PORT;
   ClientSocketBascula.Active := True;//Activates the client
    Beep;
 { if ClientSocketBascula.Active = True then
   begin

   end
   else
   begin
    EscribirMensajeAyuda('PESADA NO VALIDA','');
   end;
                }




end;

procedure TFormPrincipal.ClientSocketBAsculaConnect(Sender: TObject;
  Socket: TCustomWinSocket);
begin
 inherited;
    EscribirMensajeAyuda('COnectado BASC','');
  Socket.SendText('$');

end;


procedure TFormPrincipal.ClientSocketBAsculaError(Sender: TObject;
  Socket: TCustomWinSocket; ErrorEvent: TErrorEvent; var ErrorCode: Integer);
  var error : Integer;
begin
  inherited;
   error := ErrorCode; {prevent exception from being thrown}

		  
   ErrorCode := 0;
									  
																			  
																						  
																						  
																						  
																						  
																						  

   if error = 10049 then
     begin
     //showmessage('asynchronous socket error');
     end ;
  
end;
procedure TFormPrincipal.ClientSocketBAsculaRead(Sender: TObject;
  Socket: TCustomWinSocket);

const
    VALIDOS=['0'..'9','.'];//'A'..'Z','a'..'z','a'..'z','á','é','í','ó','ú','(',')','+','-','*','/','<','>','=','!','~','^',';','.',',','‘','@','%','“','#','$','&','_','|','{','}','?','[',']' ];

  var
   cadenarecibida, cadenatratada:String;
   i:integer;
begin
//Reads and displays the message received from the server;
 cadenarecibida:= Socket.ReceiveText;
   // MemoTCP.Text:=Memo1.Text+'Server: '+cadenarecibida+#13#10;
   cadenatratada:='';
        //Elimino los carateres no validos en PL/SQL
 for i:= 1 to Length(upperCase(cadenarecibida)) do
     if cadenarecibida[i] in VALIDOS then cadenatratada:=cadenatratada+CadenaRecibida[i];

     EscribirMensajeAyuda('Pesada '+cadenatratada,'');
  if (sender as TClientsocket).Host=TvariablesPrograma.GetInstance.BASCULA_TCPIP1 then
    Editinput.text:=Stringreplace(Editinput.text,'*0791' ,'*', [])+cadenatratada;
  if (sender as TClientsocket).Host=TvariablesPrograma.GetInstance.BASCULA_TCPIP2 then
    Editinput.text:=Stringreplace(Editinput.text,'*0792' ,'*', [])+cadenatratada;
  if (sender as TClientsocket).Host=TvariablesPrograma.GetInstance.BASCULA_TCPIP3 then
    Editinput.text:=Stringreplace(Editinput.text,'*0793' ,'*', [])+cadenatratada;
  if (sender as TClientsocket).Host=TvariablesPrograma.GetInstance.BASCULA_TCPIP4 then
    Editinput.text:=Stringreplace(Editinput.text,'*0794' ,'*', [])+cadenatratada;
  if (sender as TClientsocket).Host=TvariablesPrograma.GetInstance.BASCULA_TCPIP5 then
    Editinput.text:=Stringreplace(Editinput.text,'*0795' ,'*', [])+cadenatratada;

  Editinput.SelStart := Length(Editinput.Text);
    ClientSocketBAscula.active:=FAlse;
end;


procedure TformPrincipal.CreaSucursales(Sender:Tobject);
var
  C: TBUtton;
i,u,h, PrimeraComa,Sucursal_actual: integer;
Sucursal_Externa, Sucursal_Anadida: String;
Lista_Sucursales: TStringList;

begin

 i:=0;
 u:=0;
 h:=0;
   //  Sucursal_Externa:=Stringreplace(TvariablesPrograma.GetInstance.SUCURSALES, ' ', '_',[rfReplaceAll, rfIgnoreCase]);
      Sucursal_Externa:=TvariablesPrograma.GetInstance.SUCURSALES;
     Lista_Sucursales:= Tstringlist.Create;

    if BTNACCRapido2.Caption = 'Botones borrados' then
    begin

     while i=0 do

       begin

       PrimeraComa:=Pos(',',Sucursal_Externa);
       Sucursal_anadida:=copy(Sucursal_Externa,1,Primeracoma -1);



       if Sucursal_Externa <> '' then
        begin


                  if Sucursal_anadida = '' then
                  begin
                    Lista_Sucursales.Add(Sucursal_Externa);
                    Sucursal_actual:=Lista_Sucursales.Count mod 4;
                     if Sucursal_actual = 0 then
                    u:=735
                     else if Sucursal_actual = 1 then
                     u:=0
                    else if Sucursal_actual = 2 then
                    u:=245
                     else if Sucursal_actual = 3 then
                     u:=490;

                    if (Sucursal_actual = 1)
                     AND (Lista_Sucursales.Count > 1) then
                      h:=h+77;

                    C := TBUtton.Create(Self);
                    C.Left := 949 + u;
                    C.Top := 71 + h;
                    C.Width := 240;
                    C.Height := 75;
                    C.Visible := True;
                    c.Font.Size:= STRTOINT(TVARIABLESPROGRAMA.GetInstance.SUCURSALES_TEXT);

                    C.Parent := Self;
                    C.Name:=  copy(StringReplace(Sucursal_Externa, ' ', '_',[rfReplaceAll, rfIgnoreCase]),1,Pos('|',StringReplace(Sucursal_Externa, ' ', '_',[rfReplaceAll, rfIgnoreCase]))-1);
                    C.Caption :=copy(Sucursal_Externa,1,Pos('|',Sucursal_Externa)-1); ;
                    c.OnClick:= CustomOnClick;
                    c.hint:=copy(StringReplace(Sucursal_Externa, ' ', '_',[rfReplaceAll, rfIgnoreCase]),1,Pos('|',StringReplace(Sucursal_Externa, ' ', '_',[rfReplaceAll, rfIgnoreCase]))-1);;
                    i:=1;
                  end
                  else
                  begin
                   Lista_Sucursales.Add(Sucursal_anadida);
                   Sucursal_actual:=Lista_Sucursales.Count mod 4;
                     if Sucursal_actual = 0 then
                     u:=735
                     else if Sucursal_actual = 1 then
                     u:=0
                     else if Sucursal_actual = 2 then
                     u:=245
                     else if Sucursal_actual = 3 then
                     u:=490;

                      if (Sucursal_actual = 1)
                     AND (Lista_Sucursales.Count > 1) then
                      h:=h+77;

                    C := TBUtton.Create(Self);
                    C.Left := 949 + u;
                    C.Top := 71 + h;
                    C.Width := 240;
                    C.Height := 75;
                    C.Visible := True;
                   c.Font.Size:= STRTOINT(TVARIABLESPROGRAMA.GetInstance.SUCURSALES_TEXT);
                    C.Parent := Self; //Any container: form, panel, ... Ç
                     C.Name:=   copy(StringReplace(Sucursal_anadida, ' ', '_',[rfReplaceAll, rfIgnoreCase]),1,Pos('|',StringReplace(Sucursal_anadida, ' ', '_',[rfReplaceAll, rfIgnoreCase]))-1);
                    C.Caption :=copy(Sucursal_anadida,1,Pos('|',Sucursal_anadida)-1);
                    c.OnClick:= CustomOnClick;
                     c.hint:=copy(StringReplace(Sucursal_anadida, ' ', '_',[rfReplaceAll, rfIgnoreCase]),1,Pos('|',StringReplace(Sucursal_anadida, ' ', '_',[rfReplaceAll, rfIgnoreCase]))-1);;
                     //copy(Sucursal_anadida,Pos('|',Sucursal_anadida)+1);
                   // c.Hint:='91..00'

                  Sucursal_Externa:=copy(Sucursal_Externa,PrimeraComa+1,1000);

                  end;
        end
       else

       i:=1

       end;

        BTNACCRapido2.Caption:='Botones creados';

    end;




   //    for u := 0 to Lista_Sucursales.Count -1 do
     //   begin

     //     C := TBUtton.Create(Self);
     //     C.Left := 559 + u*20;
     //     C.Top := 56 + u*20;
     //     C.Width := 75;
     //     C.Height := 55;
     //     C.Visible := True;
       //   C.Parent := Self; //Any container: form, panel, ...
      //    C.Caption := 'True';
      //  end;
end;

procedure TformPrincipal.UsaSucursales(Sender:Tobject;info2:string);
var
Lista_Botones, Lista1: TStringList;
MiBotonSucursal, BotonMarcado: TComponent;
C: TBUtton;
i,u,h, PrimeraComa,Sucursal_actual: integer;
Sucursal_Externa, Sucursal_Anadida,Numero_Sucursal: String;
Lista_Sucursales: TStringList;
begin
//showmessage('Hago lo de las sucursales.');




 i:=0;
 u:=0;
 h:=0;
     Sucursal_Externa:=TvariablesPrograma.GetInstance.SUCURSALES;
     Lista_Sucursales:= Tstringlist.Create;


      BotonMarcado:= FindMyComponent(Application,info2);
    //  showmessage(BotonMarcado.Name);





      Numero_SucurSal:= copy(Sucursal_Externa,Pos(StringReplace(info2, '_', ' ',[rfReplaceAll, rfIgnoreCase]),Sucursal_Externa)+1+info2.Length,18);
    //  Showmessage(Numero_SucurSal);
     if Numero_SucurSal <> '910000000000000099' then
      begin
        EditINput.Clear;
        EditInput.Text:=Numero_SucurSal ;
        EditInputKeyWearable('INTRO');
    end;
   // Memolog.text:= Numero_SucurSal +#13#10 +Numero_SucurSal;



     while i=0 do

       begin

       PrimeraComa:=Pos(',',Sucursal_Externa);
       Sucursal_anadida:=copy(Sucursal_Externa,1,Primeracoma -1);



       if Sucursal_Externa <> '' then
        begin


                  if Sucursal_anadida = '' then
                  begin
                    Lista_Sucursales.Add(Sucursal_Externa);
                    MiBotonSucursal:= FindMyComponent(Application,copy(StringReplace(Sucursal_Externa, ' ', '_',[rfReplaceAll, rfIgnoreCase]),1,Pos('|',StringReplace(Sucursal_Externa, ' ', '_',[rfReplaceAll, rfIgnoreCase]))-1));
                    MiBotonSucursal.Destroy;

                    i:=1;
                  end
                  else
                  begin
                   Lista_Sucursales.Add(Sucursal_anadida);
                //   showmessage(copy(StringReplace(Sucursal_anadida, ' ', '_',[rfReplaceAll, rfIgnoreCase]),1,Pos('|',StringReplace(Sucursal_anadida, ' ', '_',[rfReplaceAll, rfIgnoreCase]))-1));

                   MiBotonSucursal:= FindMyComponent(Application,copy(StringReplace(Sucursal_anadida, ' ', '_',[rfReplaceAll, rfIgnoreCase]),1,Pos('|',StringReplace(Sucursal_anadida, ' ', '_',[rfReplaceAll, rfIgnoreCase]))-1));

                   MiBotonSucursal.Destroy;


                  Sucursal_Externa:=copy(Sucursal_Externa,PrimeraComa+1,1000);

                  end;
        end
       else

       i:=1

       end;
       BTNACCRapido2.Caption:='Botones borrados';

end;


function TformPrincipal.FindMyComponent(Parent: TComponent; Name: string): TComponent;
var
  i: integer;
begin
  if Parent.ComponentCount = 0 then exit(nil);
  Result:= Parent.FindComponent(Name);
  if Assigned(Result) then Exit;
  for i:= 0 to Parent.ComponentCount do begin
    Result:= FindMyComponent(Parent.Components[i], Name);
    if Assigned(Result) then Exit;
  end; {for i}
end;

procedure TformPrincipal.CustomOnClick(Sender: TObject);
begin
  UsaSucursales(Sender,TControl(Sender).hint);
end;

procedure TformPrincipal.CreaADVColumnasGrid (Grid:TDBAdvGrid;Origen:String;ClearCombos:Boolean);
var
 i ,u,h:integer;
 salidaList:TstringList;
 FicheroGrid:TstringList;
 ListasGrid:TstringList;
 numerocolumna,valorADD:String;
  ESCheckBox:boolean;
begin
   FicheroGrid:= TStringList.Create;
   ListasGrid:= TStringList.Create;

   if Origen='' then
     Origen:=Grid.name;

try
   with FicheroGrid do
   BEGIN
       LoadFromFile(TVariablesPrograma.GetInstance.INIgridColumnas);
      SalidaList := TStringList.Create;

     ////GUARDO LOS DESPLEGABLES
     ///


         for u:=1 to Grid.Columns.Count do
         begin
           if pos('Combo',Grid.Columns[u-1].name)>0 then
           begin
             for h:=0 to Grid.Columns[u-1].ComboItems.count-1 do
             begin
                   listasGrid.Add(inttostr(u-1)+'|'+Grid.Columns[u-1].comboitems[h]);
             end;

           end;


         end;

       if grid.Columns.Count>0 then
       begin
        if grid.Columns[0].CheckBoxField = true then
          ESCheckBox:=true;
       end ;
        Grid.Columns.clear;

      if ESCheckBox then
         begin

          grid.Columns.Clear;
          grid.Columns.Insert(0);
          grid.Columns[0].CheckBoxField := true;
          grid.Columns[0].ColumnPopupType := cpNormalCellsLClick;
          grid.Columns[0].editor := edDataCheckBox;
        end;
      i := 0;
      while i < FicheroGrid.count do
      begin
        SalidaList.Clear;
        if FicheroGrid.Strings[i] <>'' then
        begin

          FicheroGrid.Strings[i]:=separaCadenas(FicheroGrid.Strings[i],SalidaList);
            IF (copy(SalidaList[1],1,1) <>';') and (SalidaList[1]=Origen) THEN
               BEGIN
                Grid.Columns.Add;

                  Grid.Columns[Grid.Columns.Count-1].FieldName:=SalidaList[2];
                  Grid.Columns[Grid.Columns.Count-1].HEADER := SalidaList[3];
                  Grid.Columns[Grid.Columns.Count-1].name := SalidaList[4]+inttostr(Grid.Columns.Count-1);

                  Grid.Columns[Grid.Columns.Count-1].Editor := TEditorType.edNormal;

                 if SalidaList[4]='taCenter' then
                     Grid.Columns[Grid.Columns.Count-1].Alignment:=TAlignment(taCenter)
                     else
                  if SalidaList[4]='taLeft' then
                     Grid.Columns[Grid.Columns.Count-1].Alignment :=TAlignment(taLeftJustify)
                     else
                  if SalidaList[4]='taRigth' then
                     Grid.Columns[Grid.Columns.Count-1].Alignment :=TAlignment(taRightJustify)
                 else
                  if SalidaList[4]='taCenterEllipsis' then
                   begin
                     Grid.Columns[Grid.Columns.Count-1].Alignment:=TAlignment(taCenter);
                     //Grid.Columns[Grid.Columns.Count-1].buttonStyle:=cbsEllipsis;
                   end
                     else
                  if SalidaList[4]='taLeftEllipsis' then
                  begin
                     Grid.Columns[Grid.Columns.Count-1].Alignment :=TAlignment(taLeftJustify);
                     //Grid.Columns[Grid.Columns.Count-1].buttonStyle:=cbsEllipsis;
                  end
                  else
                  if SalidaList[4]='taRigthEllipsis' then
                  begin
                     Grid.Columns[Grid.Columns.Count-1].Alignment :=TAlignment(taRightJustify);
                     //Grid.Columns[Grid.Columns.Count-1].buttonStyle:=cbsEllipsis;

                end;

                 if SalidaList[4]='taLeftCombo' then
                  begin
                    Grid.Columns[Grid.Columns.Count-1].Alignment :=TAlignment(taLeftJustify);
                    Grid.Columns[Grid.Columns.Count-1].Editor := TEditorType.edComboEdit;
                  end;

                  if SalidaList[4]='taLeftLink' then
                  begin
                    Grid.Columns[Grid.Columns.Count-1].Alignment :=TAlignment(taLeftJustify);
                    Grid.Columns[Grid.Columns.Count-1].font.color:=$00FF8000;
                    Grid.Columns[Grid.Columns.Count-1].font.Style:=Grid.Columns[Grid.Columns.Count-1].font.Style+[fsUnderline];

                  end;



                  Grid.Columns[Grid.Columns.Count-1].Width     := strtoint(SalidaList[5]);
                 // Grid.Columns[Grid.Columns.Count-1].hEADERAlignment := TAlignment(taCenter);
                 // Grid.Columns[Grid.Columns.Count-1].hEADERfont.color:=$00C08000;
                //  Grid.Columns[Grid.Columns.Count-1].hEADERfont.Size:=12;
                 // Grid.Columns[Grid.Columns.Count-1].font.Size:=12;
                //  Grid.Columns[Grid.Columns.Count-1].font.color:=$00FF8000;


                end;

          end;

         INC(i);
      end;
   END;
        //Grid.FixedCols:=1;
        // Grid.FixedColWidth:=0;
        Grid.PageMode := False;
     Grid.SortSettings.Show := True;
     Grid.SortSettings.IgnoreBlanks  := True;
     Grid.SortSettings.BlankPos  := blLast;
     //###### AGRUPACION EN EL GRID

      Grid.Grouping.MergeHeader := true;//POner en cabecera el valor del campo por el que se agrupa







     //####  FIN DE AGRUPACION EN EL GRID

     //advstringgrid1.SubGroup(1);
     Grid.ContractAll;



                 u:=0;
                    while u < ListasGrid.count-1 do
                    begin
                        numerocolumna:=copy(ListasGrid.Strings[u],1,pos('|',ListasGrid.Strings[u])-1) ;
                        valorADD:= copy(ListasGrid.Strings[u],pos('|',ListasGrid.Strings[u])+1,200) ;
                        Grid.Columns[strtoint(numerocolumna)].ComboItems.Add(valorADD);

                      inc(u);
                    end;


   finally
      freeandnil(fICHEROgRID);
      freeandnil(SalidaList);
      freeandnil(ListasGrid);
    end;
end;

Function TformPrincipal.IS_ADV_GridHeaderColumn (Grid:TDBAdvGrid):Boolean;
var
 Y :integer;
 valoractual, valorultimo:String;

begin
     //08/08/19 solo se me ocurre que si todos los valores de la fila seleccionada tienen un
     //mismo valor entonces es una cabecera , en el momento havya valores diferentes en cada campo
     //es una fila norma seguro.

     if grid.FixedCols=0 then //Si no tiene columna fija directamente no existe el grupo
      result:=false
     else
     begin
         valorultimo:='XXX';
         RESULT:=True;
         for y:= 1 to grid.Columns.Count-1 do  //y:=1 p ad ara saltarme la columna fija
         begin
         //   showmessage( inttostr(y)+'_'+grid.Columns[y].FieldName+'(Busx '+Grid.Columns[y].Rows[Grid.SelectedRow[0]]);
              valoractual:= Grid.Columns[y].Rows[Grid.SelectedRow[0]];
              if (valoractual<>'') and (valorultimo<>'XXX') and (valorultimo<>valoractual) then
              begin
                RESULT:=False;
              end;
              valorultimo:=valoractual;

         end;
     end;


end;

procedure TFormPrincipal.MemoAyudaGesture(Sender: TObject;
  const EventInfo: TGestureEventInfo; var Handled: Boolean);
var
   formControl: TFormAuxBasico;
   S:String;
begin
 inherited;
  formControl := FrmAuxBasico;
 if GestureToIdent(EventInfo.GestureID, S) then
 begin
  //COMENTADO 17/09/19 por Abstract Error  formControl.EjecutaGesto(S);
      //ShowMessage(S) ;
 end;

end;



procedure TformPrincipal.AnadeBultoalaCola(Amatricula, Apedido, ANombre_Cliente:String);

var
 i:integer;
 encontrado:boolean;
begin
  //
end;


function TformPrincipal.DAmeProximaMatricula:String;
var
 u:integer;
 encontrado:boolean;
begin

      Result:='';

end;

Procedure TformPrincipal.BOrraMatricula(AmatriculaBorrar,ACadenaCB_PdteValidar:String);
var
 i:integer;
 encontrado:boolean;
begin
   // listboxlecturas.Items.Delete(listboxlecturas.Items.IndexOf(AmatriculaBorrar));

end;



function TformPrincipal.DAmeNumeroItems:integer;
var
 u:integer;
 encontrado:boolean;
begin

     Result:=1;//listboxlecturas.Items.Count;

end;

procedure TformPrincipal.LimpiarCola;
var
 i:integer;
 encontrado:boolean;
begin
//  listboxlecturas.Clear;
end;








Procedure TformPrincipal.DameDatosAdicionales(ASSCC:string; var APedidoVenta, AOrdenPicking, ANombre_Cliente:string);
var
 QnumeroPedido: Tuniquery;
begin



  QnumeroPedido := TUniQuery.Create(Self);
  QnumeroPedido.Connection := DataMBasico.Sesion;

  QnumeroPedido.CLOSE;
  QnumeroPedido.SQL.Add('select * ');
  QnumeroPedido.SQL.Add(' from v_fp05_lista_bulto_pale ');
  QnumeroPedido.SQL.Add(' where SSCC = ' + QuotedStr(Asscc) );
  QnumeroPedido.OPEN;

  APedidoVenta:= QnumeroPedido.FieldByName('PEDIDO_VENTA').AsString;
  AOrdenPicking:= QnumeroPedido.FieldByName('ORDEN_PICKING').AsString;
  ANombre_Cliente:=QnumeroPedido.FieldByName('NOMBRE_CLIENTE').AsString;


end;

function TformPrincipal.Convierte91 (ACadena:String):String;
var
SufijoPuntoPunto,cadenadeceros:String;
  longitudSufijoPuntoPunto:integer;
begin

             SufijoPuntoPunto:= ACAdena;
             longitudSufijoPuntoPunto:=length(SufijoPuntoPunto);
             cadenadeceros:=RellenaCAdena(SufijoPuntoPunto,16,'0','I');
             result:='91'+cadenadeceros;

end;


procedure TformPrincipal.ArrancaTarjetaMesurex;
begin
    //

end;

procedure TformPrincipal.TarjetaMesurex_Enviarunbit(Encender:integer);
    begin
    //

end;

procedure TformPrincipal.ActivaExpulsor(Aexpulsor:integer);
    begin
    //

end;

procedure TformPrincipal.MostrarPantallaCambiarExpulsores;
    begin
    //

end;

procedure TformPrincipal.REINICIARExpulsor9;
begin
    //

end;

function TformPrincipal.DAmeProximaMatriculaComprobacion:String;
var
 u:integer;
 encontrado:boolean;
begin

      Result:='';

end;

Procedure TformPrincipal.BOrraMatriculaComprobacion(AmatriculaBorrar:String);
begin
  //
end;


Procedure TFormPrincipal.ImprimeTrabajo_IntroduceLogo(AplantillaLoGO,AFichero:String);

var
  FicheroLogo,unirficheros: string;
  salidalogo: TextFile;
  v,t,LineaLogo: Integer;
  StringLogo:TStringList;
   Fichero_TML1,Fichero_TML2, EXT_FICHEROs_tml:sTRING;
   FicheroOriginal:String;
    diferenciaProceso,DifSegundos:real;
   horaantes:Tdatetime;
begin
   EXT_FICHEROs_tml:=Tvariablesprograma.GetInstance.DamefechaHOrafichero+'_'+ Tvariablesprograma.GetInstance.Terminal;


  ficherologo:=ChangeFileExt(AFichero,'.logo');
  //CREO COPIA FICHERO LOGO
   FicheroOriginal:=Stringreplace(ficherologo, EXT_FICHEROs_tml,'',[rfReplaceAll, rfIgnoreCase]);
   copyfile(pchar(AplantillaLoGO),pchar(ficherologo), true);

   //

               if not fileexists(ficherologo) then
                BEGIN
                //FormPrincipal._Alert('No Encuentro Logo:'+ficherologo,mtWarning,[mbOK ],['Aceptar']);
                //showmessage   ('No Encuentro Logo:'+ficherologo);
                exit;
                END;
  //0.- Cojo Linea que lleva la palabra LOGX1
  Fichero_TML1:=IncludeTrailingPathDelimiter(extractfilepath(afichero))+ChangeFileExt(ExtractFileName(afichero), '')+'_1'+'.'+EXT_FICHEROs_tml;
  Fichero_TML2:=IncludeTrailingPathDelimiter(extractfilepath(afichero))+ChangeFileExt(ExtractFileName(afichero), '')+'_2'+'.'+EXT_FICHEROs_tml;

                  StringLogo := TStringList.Create;
                  StringLogo.LoadFromFile(AFichero);
                  v:=0;
                  lineaLogo:=0;
                   while v < StringLogo.Count do
                     begin
                       if pos ( 'LOGX1=',StringLogo.Strings[v] ) >0 then
                       begin
                            lineaLogo:=v;
                       end;
                      inc(v);
                     end;

                 if linealogo=0 then
                BEGIN
                freeandnil(StringLogo);
                exit;
                END;

  //1.- -Abro el Fichero y le meto las lineas hasta antes del Logo
                AssignFile(salidalogo,Fichero_TML1);
                rewrite(salidalogo);
                           for t := 0 to lineaLogo-1 do
                           begin
                             writeln(salidalogo, StringLogo.Strings[t] );
                           end;
                closefile(salidalogo);


  //2.  Creo el REsto del Fichero
                AssignFile(salidalogo,Fichero_TML2);
                rewrite(salidalogo);
                        for t := lineaLogo+1 to StringLogo.Count-1 do
                           begin
                             writeln(salidalogo, StringLogo.Strings[t] );
                           end;
                closefile(salidalogo);
  //3.-uNO TODOS LOS FICHERO
      deletefile( AFichero);
      unirficheros:='copy /b '+Fichero_TML1+'+'+ficherologo+'+'+Fichero_TML2+' '+Afichero;

      AssignFile(salidalogo, 'salidalogo'+EXT_FICHEROs_tml+'.bat');
      rewrite(salidalogo);
      writeln(salidalogo, unirficheros);
      closefile(salidalogo);
     horaantes:=now;
    while DifSegundos<1 do
     begin
             diferenciaProceso:=SecondSpan(horaantes,now);

             DifSegundos:=SecondSpan(GetFilemODIFYTime('salidalogo'+EXT_FICHEROs_tml+'.bat'),now);

          if not FileExists('salidalogo'+EXT_FICHEROs_tml+'.bat') and (diferenciaProceso > 20 ) then//si en ocho segundos no sale
               DifSegundos:=diferenciaProceso;


     end;
      ShellExecute(0,'open',PWideChar('salidalogo'+EXT_FICHEROs_tml+'.bat'),nil,pwidechar(extractfilepath(application.ExeName)),SW_MINIMIZE);
  //    deletefile( Fichero_TML1);
  //    deletefile( Fichero_TML2);
    //  deletefile('salidalogo'+EXT_FICHEROs_tml+'.bat');

      freeandnil(StringLogo);
end;

procedure TFormPrincipal.UnirArchivoNEW(Lista: TStringList;sArchivoFinal: String);

var
  unirficheros,ListaDeFicheros: string;
  salidalogo: TextFile;
  nombreficherosalida,sArchivoFinalRename:sTRING;
  POSLOGX1:integer;
  i:integer;
  diferenciaProceso,DifSegundos:real;
   horaantes:Tdatetime;
begin

                //3.-uNO TODOS LOS FICHERO
                    //sArchivoFinalRename:=changefileext( sArchivoFinal,'.TMP');
                    //copyfile(pchar(sArchivoFinal),pchar(sArchivoFinalRename), true);
                    //deletefile( sArchivoFinal);

                  //  unirficheros:='copy /b '+sArchivoAUnir+'+'+sArchivoFinalRename+' '+sArchivoFinal;
                    ListaDeFicheros:='';
                    i:=0;
                      while i < Lista.Count do
                     begin
                      if ListaDeFicheros='' then
                      ListaDeFicheros:=lista[i]
                      else
                       ListaDeFicheros:=ListaDeFicheros+ ' + '+Lista[i];

                       Inc(i);
                     end;


                      unirficheros:='copy /b '+ListaDeFicheros+' '+sArchivoFinal;


                    nombreficherosalida:= 'salidaUNIDA'+'_'+Tvariablesprograma.GetInstance.DamefechaHOrafichero+'.bat';
                    AssignFile(salidalogo, nombreficherosalida );
                    rewrite(salidalogo);
                    writeln(salidalogo, unirficheros);
                    closefile(salidalogo);

                      horaantes:=now;

     while DifSegundos<2 do
     begin
             diferenciaProceso:=SecondSpan(horaantes,now);

             DifSegundos:=SecondSpan(GetFilemODIFYTime(nombreficherosalida),now);

          if not FileExists(nombreficherosalida) and (diferenciaProceso > 8 ) then//si en ocho segundos no sale
               DifSegundos:=diferenciaProceso;


     end;

                    ShellExecute(0,'open',PWideChar(nombreficherosalida),nil,pwidechar(extractfilepath(application.ExeName)),SW_MINIMIZE);


                //    deletefile( Fichero_TML1);
                //    deletefile( Fichero_TML2);
                  //  deletefile('salidalogo'+EXT_FICHEROs_tml+'.bat');


end;


procedure TFormPrincipal.Arranca_Thread_Lector_IN;
begin
              //
end;

procedure TFormPrincipal.Arranca_Thread_Lector_OUT;
begin
//
end;


procedure TFormPrincipal.ActivaExpulsor7;
begin
 //
end;

procedure TFormPrincipal.DesactivaTodosLosExpulsores;
begin
  //
end;


procedure TFormPrincipal.Arranca_PARA_Thread_TTimerComprobacion(Arranca,Identificador:String);
begin
//
end;

procedure TFormPrincipal.Showalert(Amensaje:String);
begin

//   _Alert(Amensaje,mtWarning,[mbOK ],['Aceptar']);

//
end;

procedure TFormPrincipal.PrintTextFroMservice(Aimpresora,Afilename:String);

  const
  FONT_NAME = 'Times New Roman';
  FONT_SIZE = 10;
var
  ADevice, ADeviceName, ADevicePort: array[0..255]of Char;
  PrinterHandle: THandle ;
  dwJob: cardinal;
  dwBytesWritten: cardinal;
  AUtf8: UTF8string;

  MARGIN: integer;
  sl: TStringList;
  i, h: Integer;
  r, rFooter: TRect;
  s: string;
  DocEnd: integer;
  Numbering:boolean;
    ADeviceMode: THandle;
begin




  //your printer (a windows generic printer works fine)
  Numbering:=True;

  Printer.PrinterIndex := GetLabelPrinterIndex(Aimpresora);
  Printer.GetPrinter(ADevice, ADeviceName, ADevicePort, ADeviceMode);


  if SpoolFile(Afilename, Printer.Printers[Printer.PrinterIndex]) = 0 then
   // MemoAyuda.Lines.Insert(0,'Imp.'+ Aimpresora+' File'+ Afilename)
  else
    MemoAyuda.Lines.Insert(0,'ERROR Imp.'+ Aimpresora+' File'+ Afilename)

  {    sl:= TStringList.Create;
  try
  sl.LoadFromFile(Afilename);
    Printer.BeginDoc;
    Printer.Title := Afilename; // or application name or sth else
    Printer.Canvas.Font.Name := FONT_NAME;
    Printer.Canvas.Font.Size := FONT_SIZE;
    MARGIN := 5*Printer.Canvas.TextWidth('M');
    DocEnd := Printer.PageHeight - MARGIN;
    if Numbering then
    begin
      dec(DocEnd, 2*Printer.Canvas.TextHeight('8'));
      rFooter := Rect(0, DocEnd, Printer.PageWidth, Printer.PageHeight - MARGIN);
      DrawText(Printer.Canvas.Handle,
        PChar(IntToStr(Printer.PageNumber)),
        length(IntToStr(Printer.PageNumber)),
        rFooter,
        DT_SINGLELINE or DT_CENTER or DT_BOTTOM);
    end;
    r.Left := MARGIN;
    r.Top := MARGIN;
    for i := 0 to sl.Count - 1 do
    begin
      r.Right := Printer.PageWidth - MARGIN;
      r.Bottom := DocEnd;
      s := sl.Strings[i];
      if s = '' then s := ' ';
      h := DrawText(Printer.Canvas.Handle, // Height of paragraph on paper
        PChar(s),
        length(s),
        r,
        DT_LEFT or DT_TOP or DT_WORDBREAK or DT_CALCRECT);
      if r.Top + h >= DocEnd then
      begin
        Printer.NewPage;
        if Numbering then
          DrawText(Printer.Canvas.Handle,
            PChar(IntToStr(Printer.PageNumber)),
            length(IntToStr(Printer.PageNumber)),
            rFooter,
            DT_SINGLELINE or DT_CENTER or DT_BOTTOM);
        r.Top := MARGIN;
        r.Bottom := DocEnd;
      end;
      if h > Printer.PageHeight - 2*MARGIN then
        raise Exception.Create('Line too long to fit on single page.');
      DrawText(Printer.Canvas.Handle,
        PChar(s),
        length(s),
        r,
        DT_LEFT or DT_TOP or DT_WORDBREAK);
      inc(r.Top, h);
    end;
    Printer.EndDoc;

  finally
    sl.Free;
  end;
   }

end;

function TFormPrincipal.GetLabelPrinterIndex(AnombreImprsora:String): Integer;
var
  i: Integer;
begin
  for i := 0 to Printer.Printers.Count - 1 do
    if AnsiContainsText(Printer.Printers[i], AnombreImprsora) then
    begin
      Exit(i);
    end;
  Result := -1;
end;

function TFormPrincipal.SpoolFile(const FileName, PrinterName: string): Integer;
var
  Buffer: record
    JobInfo: record // ADDJOB_INFO_1
      Path: PChar;
      JobID: DWORD;
    end;
    PathBuffer: array[0..255] of Char;
  end;
  SizeNeeded: DWORD;
  Handle: THandle;
  PrtName: string;
  ok: Boolean;
   ADevice, ADeviceName, ADevicePort: array[0..255]of Char;
    ADeviceMode: THandle;

begin
  // Flush job to printer MemoAyuda.Lines.Insert(0,'ERROR1');


  PrtName := PrinterName;
  // MemoAyuda.Lines.Insert(0,'ERROR1'+ inttostr(Printer.PrinterIndex));

if PrtName = '' then
 begin
    PrtName := Printer.Printers[Printer.PrinterIndex]; // Default printer name ok := False;
    //  MemoAyuda.Lines.Insert(0,'ERROR2');
 end;

  if OpenPrinter(PChar(PrtName), Handle, nil) then
  begin

        // MemoAyuda.Lines.Insert(0,'NERROR3');
        if not AddJob(Handle, 1, @Buffer, SizeOf(Buffer), SizeNeeded) then
        begin
          MemoAyuda.Lines.Insert(0,'NERROR4 '+Buffer.JobInfo.Path);
        end
        else
        begin


           if not CopyFile(PChar(FileName), Buffer.JobInfo.Path, true )then
           begin
               MemoAyuda.Lines.Insert(0,'NERROR5');
           end
           else
             begin

                  if not ScheduleJob(Handle, Buffer.JobInfo.JobID) then
                  begin
                    MemoAyuda.Lines.Insert(0,'NERROR6');
                  end
                  else
                  begin

                    ok := True;
                     MemoAyuda.Lines.Insert(0,'Imp.'+ PrtName);
                 end;

             end;
        end;


    end;


        if not ok then

          Result := GetLastError
        else
          Result := 0;


          PrtName :='';
end;




end.
