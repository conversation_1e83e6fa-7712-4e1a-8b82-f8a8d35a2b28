:root {
  --primary: #008bcc;
  --accent: #f5f6fa;
  --text: #222;
  --menu-bg: #fff;
  --menu-option-bg: #e6f2fa;
  --card-bg: #fff;
  --card-shadow: 0 2px 8px rgba(0,0,0,0.08);
  --border-radius: 1rem;
  --menu-max-width: 420px;
}

body {
  margin: 0;
  font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
  background: var(--accent);
  color: var(--text);
}

* {
  box-sizing: border-box;
}

input, button {
  font-family: inherit;
}

.menu-mobile-wrapper {
  max-width: var(--menu-max-width);
  margin: 0 auto;
  padding: 16px 8px 32px 8px;
}
.menu-header, .ubicacion-header, .detalle-ubicacion-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 18px;
  width: 100%;
  min-height: 48px;
}
.menu-header h2, .ubicacion-header h3, .detalle-ubicacion-header h3 {
  color: var(--primary);
  margin: 0 auto;
  font-size: 2.1rem;
  font-weight: 800;
  letter-spacing: 1px;
  text-align: center;
  flex: 1 1 auto;
}
.menu-header small {
  color: #888;
  margin-left: 12px;
}
.ubicacion-header h3, .detalle-ubicacion-header h3 {
  font-size: 2.1rem;
}
.menu-btn-back, .ubicacion-btn-back, .detalle-ubicacion-btn-back {
  background: linear-gradient(90deg, var(--primary) 60%, #00b4d8 100%);
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 8px 18px;
  font-size: 1.08rem;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,139,204,0.10);
  transition: background 0.2s, transform 0.1s;
  margin-right: auto;
}
.menu-btn-back:active, .menu-btn-back:hover,
.ubicacion-btn-back:active, .ubicacion-btn-back:hover,
.detalle-ubicacion-btn-back:active, .detalle-ubicacion-btn-back:hover {
  background: #005c8a;
  transform: scale(0.97);
}
@media (max-width: 600px) {
  .menu-header, .ubicacion-header, .detalle-ubicacion-header {
    min-height: 36px;
    margin-bottom: 12px;
    gap: 4px;
  }
  .menu-header h2, .ubicacion-header h3, .detalle-ubicacion-header h3 {
    font-size: 1.3rem;
  }
  .menu-btn-back, .ubicacion-btn-back, .detalle-ubicacion-btn-back {
    font-size: 0.98rem;
    padding: 7px 8px;
  }
}

.menu-input-box {
  display: flex;
  justify-content: center;
  margin-bottom: 22px;
}
.menu-input-box input, .ubicacion-input-box input, .detalle-ubicacion-input-box input {
  padding: 18px 22px;
  border-radius: 14px;
  border: 2px solid var(--primary);
  font-size: 2rem;
  width: 100%;
  max-width: 240px;
  text-align: center;
  outline: none;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,139,204,0.04);
  font-weight: bold;
  letter-spacing: 2px;
  transition: border 0.18s, box-shadow 0.15s;
}
.menu-input-box input:focus, .ubicacion-input-box input:focus, .detalle-ubicacion-input-box input:focus {
  border-color: #005c8a;
}
@media (max-width: 600px) {
  .menu-input-box input, .ubicacion-input-box input, .detalle-ubicacion-input-box input {
    font-size: 1.5rem;
    padding: 12px 6px;
    max-width: 98vw;
  }
}
.menu-input-box input:focus {
  border-color: #005c8a;
}
.menu-options {
  display: flex;
  flex-wrap: wrap;
  gap: 18px;
  justify-content: center;
}
.menu-card {
  background: var(--card-bg);
  box-shadow: var(--card-shadow);
  border-radius: var(--border-radius);
  padding: 26px 18px 22px 18px;
  min-width: 120px;
  width: 46vw;
  max-width: 170px;
  text-align: center;
  color: var(--primary);
  font-weight: 700;
  font-size: 1.15rem;
  cursor: pointer;
  border: 2px solid var(--primary);
  transition: background 0.2s, border 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  user-select: none;
}

/***** Mejoras tarjetas UbicacionMercancia *****/
.ubicacion-card-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  padding-bottom: 12px;
  width: 100%;
  box-sizing: border-box;
  margin: 0 auto;
}
.ubicacion-card {
  background: linear-gradient(120deg, #e6f2fa 70%, #fff 100%);
  box-shadow: 0 4px 24px rgba(0,139,204,0.09);
  border-radius: 18px;
  border: 1.5px solid #b8e3fa;
  min-width: 230px;
  max-width: 330px;
  min-height: 82px;
  padding: 13px 14px 13px 14px;
  color: var(--primary);
  font-weight: 700;
  font-size: 1.05rem;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 7px;
  user-select: none;
  transition: box-shadow 0.15s, border 0.18s;
  overflow: hidden;
  word-break: break-word;
}
.ubicacion-card:hover, .ubicacion-card:active {
  border-color: #008bcc;
  box-shadow: 0 6px 24px rgba(0,139,204,0.13);
}
.ubicacion-card-row {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: flex-start;
  width: 100%;
}
.ubicacion-card-row-header {
  font-size: 1.02rem;
  font-weight: 700;
  color: #0073a8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
.ubicacion-card-row-header span {
  min-width: 0;
  max-width: 100%;
  width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  display: block;
  margin-bottom: 0.5px;
  line-height: 1.19;
}
.ubicacion-card-row-header .sscc {
  font-size: 0.97rem;
  font-weight: 500;
  color: #005c8a;
  white-space: nowrap;
  overflow-x: auto;
  text-overflow: ellipsis;
  display: block;
  margin-bottom: 0.5px;
  width: 100%;
  max-width: 100%;
}
.ubicacion-card-row-header .matexterna {
  font-size: 0.97rem;
  font-weight: 500;
  color: #0073a8;
  word-break: break-all;
  white-space: normal;
  margin-bottom: 0.5px;
}
.ubicacion-card-row-header .doc {
  font-size: 1.03rem;
  font-weight: 700;
  color: #008bcc;
  margin-bottom: 0.5px;
}
.ubicacion-card-row-header {
  margin-bottom: 4px;
  border-bottom: 1px solid #d0eafd;
  padding-bottom: 2px;
}
.ubicacion-card-row-header .doc,
.ubicacion-card-row-header .sscc,
.ubicacion-card-row-header .matexterna {
  font-size: 1.01rem;
  font-weight: 700;
  color: #008bcc;
}
.ubicacion-card-row-data {
  font-size: 0.99rem;
  color: #222;
  gap: 2px;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}
.ubicacion-card-row-data span {
  min-width: 0;
  max-width: 100%;
  width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  display: block;
  margin-bottom: 0px;
  line-height: 1.2;
}
@media (max-width: 600px) {
  .ubicacion-card-list {
    gap: 10px 0;
    padding-bottom: 8px;
  }
  .ubicacion-card {
    min-width: 95vw;
    max-width: 99vw;
    font-size: 0.99rem;
    padding: 10px 3vw 8px 3vw;
  }
  .ubicacion-card-row-header span,
  .ubicacion-card-row-data span {
    max-width: 38vw;
    font-size: 0.95rem;
  }
}

.menu-card:active, .menu-card:hover {
  background: var(--menu-option-bg);
  border-color: #005c8a;
}
.menu-card-code {
  font-size: 1.2rem;
  color: #888;
  margin-bottom: 8px;
  font-weight: 900;
  letter-spacing: 1.5px;
}
.menu-card-label {
  font-size: 1.13rem;
}

@media (max-width: 600px) {
  .menu-mobile-wrapper {
    padding: 10px 2vw 24px 2vw;
    max-width: 100vw;
  }
  .menu-header h2 {
    font-size: 1.5rem;
  }
  .menu-input-box input {
    font-size: 1.5rem;
    padding: 12px 6px;
    max-width: 98vw;
  }
  .menu-card {
    min-width: 44vw;
    width: 98vw;
    max-width: 98vw;
    font-size: 1rem;
    padding: 18px 6px 14px 6px;
  }
  .menu-card-code {
    font-size: 1rem;
  }
  .menu-card-label {
    font-size: 1rem;
  }
  .menu-options {
    gap: 10px;
  }
}

/***** Ubicación Mercancía - Mobile First y Profesional *****/
.ubicacion-wrapper {
  max-width: 500px;
  margin: 0 auto;
  padding: 12px 8px 36px 8px;
}
.ubicacion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.ubicacion-header h3 {
  color: var(--primary);
  margin: 0;
  font-size: 1.4rem;
  font-weight: 800;
  letter-spacing: 1px;
}
.ubicacion-btn-back {
  background: linear-gradient(90deg, var(--primary) 60%, #00b4d8 100%);
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 10px 22px;
  font-size: 1.15rem;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,139,204,0.10);
  transition: background 0.2s, transform 0.1s;
}
.ubicacion-btn-back:active, .ubicacion-btn-back:hover {
  background: #005c8a;
  transform: scale(0.97);
}
.ubicacion-card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 18px;
  justify-content: center;
  margin-bottom: 18px;
}
.ubicacion-card-pro {
  background: linear-gradient(120deg, #e6f2fa 70%, #fff 100%);
  box-shadow: 0 4px 24px rgba(0,139,204,0.09);
  border-radius: 18px;
  padding: 14px 11px 11px 11px;
  width: 100%;
  max-width: 480px;
  color: var(--text);
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 1.13rem;
  font-weight: 500;
  border: 1.5px solid #b8e3fa;
  user-select: none;
  transition: box-shadow 0.15s;
  box-sizing: border-box;
  margin: 0 auto;
}
.ubicacion-card-pro:active, .ubicacion-card-pro:hover {
  box-shadow: 0 6px 32px rgba(0,139,204,0.17);
  border: 1.5px solid var(--primary);
}
.ubicacion-card-row-header {
  display: flex;
  justify-content: space-between;
  font-size: 1.08rem;
  font-weight: 700;
  color: var(--primary);
  border-bottom: 1px solid #cce8f7;
  padding-bottom: 5px;
  margin-bottom: 2px;
  gap: 4px;
}
.ubicacion-card-row-data {
  display: flex;
  justify-content: space-between;
  font-size: 1.09rem;
  gap: 8px;
}
.ubicacion-card-row span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.empty-cell {
  color: #bbb;
  font-style: italic;
}
.ubicacion-table-wrapper {
  width: 100%;
  overflow-x: auto;
  margin-top: 10px;
}
.ubicacion-table-pro {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,139,204,0.09);
  font-size: 1.11rem;
  overflow: hidden;
}
.ubicacion-table-pro th, .ubicacion-table-pro td {
  padding: 14px 8px;
  text-align: left;
}
.ubicacion-table-pro th {
  background: linear-gradient(90deg, #e6f2fa 80%, #fff 100%);
  color: var(--primary);
  font-size: 1.1rem;
  font-weight: 800;
  letter-spacing: 1px;
  border-bottom: 2px solid #cce8f7;
}
.ubicacion-table-pro tr {
  border-bottom: 1.5px solid #e6f2fa;
  transition: background 0.1s;
}
.ubicacion-table-pro tr:hover {
  background: #f0faff;
}
.ubicacion-table-pro td {
  font-size: 1.07rem;
  color: #222;
  border-bottom: 1px solid #f6fafd;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ubicacion-table-pro td:last-child, .ubicacion-table-pro th:last-child {
  text-align: right;
}
@media (max-width: 600px) {
  .ubicacion-wrapper {
    padding: 8px 2vw 20px 2vw;
    max-width: 100vw;
  }
  .ubicacion-header h3 {
    font-size: 1.08rem;
  }
  .ubicacion-btn-back {
    font-size: 0.98rem;
    padding: 7px 8px;
  }
  .ubicacion-card-pro {
    width: 100%;
    max-width: 100%;
    font-size: 0.99rem;
    padding: 10px 3vw 8px 3vw;
    box-sizing: border-box;
    margin: 0;
  }
  .ubicacion-card-list {
    gap: 8px;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
  }
  .ubicacion-table-pro th, .ubicacion-table-pro td {
    padding: 8px 3px;
    font-size: 0.96rem;
    overflow-wrap: break-word;
    word-break: break-word;
    white-space: normal;
  }
  .ubicacion-table-pro {
    font-size: 0.96rem;
  }
}

/***** Detalle Ubicación - Mobile First y Profesional *****/
.detalle-ubicacion-wrapper {
  max-width: 500px;
  margin: 0 auto;
  padding: 14px 8px 36px 8px;
}
.detalle-ubicacion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.detalle-ubicacion-header h3 {
  color: var(--primary);
  margin: 0;
  font-size: 1.3rem;
  font-weight: 800;
  letter-spacing: 1px;
}
.detalle-ubicacion-btn-back {
  background: linear-gradient(90deg, var(--primary) 60%, #00b4d8 100%);
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 8px 18px;
  font-size: 1.08rem;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,139,204,0.10);
  transition: background 0.2s, transform 0.1s;
}
.detalle-ubicacion-btn-back:active, .detalle-ubicacion-btn-back:hover {
  background: #005c8a;
  transform: scale(0.97);
}
.detalle-ubicacion-input-box {
  display: flex;
  justify-content: center;
  margin-bottom: 18px;
}
.detalle-ubicacion-input-box input {
  padding: 18px 22px;
  border-radius: 14px;
  border: 2px solid var(--primary);
  font-size: 2rem;
  width: 100%;
  max-width: 240px;
  text-align: center;
  outline: none;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,139,204,0.04);
  font-weight: bold;
  letter-spacing: 2px;
}
@media (max-width: 600px) {
  .detalle-ubicacion-input-box input {
    font-size: 1.5rem;
    padding: 12px 6px;
    max-width: 98vw;
  }
}
.detalle-ubicacion-card {
  background: linear-gradient(120deg, #e6f2fa 70%, #fff 100%);
  box-shadow: 0 4px 24px rgba(0,139,204,0.09);
  border-radius: 18px;
  padding: 18px 14px 14px 14px;
  color: var(--text);
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 1.13rem;
  font-weight: 500;
  border: 1.5px solid #b8e3fa;
  margin-bottom: 12px;
  user-select: none;
  transition: box-shadow 0.15s;
}
.detalle-ubicacion-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  font-size: 1.07rem;
}
.detalle-ubicacion-row-header {
  font-size: 1.01rem;
  font-weight: 700;
  color: var(--primary);
  border-bottom: 1px solid #cce8f7;
  padding-bottom: 3px;
  margin-bottom: 2px;
  gap: 8px;
}
.detalle-ubicacion-row-main {
  font-size: 1.2rem;
  font-weight: 800;
  color: #222;
  gap: 18px;
}
.detalle-u {
  color: #005c8a;
  font-size: 1.18rem;
}
.detalle-cod {
  color: #222;
  font-size: 1.18rem;
  letter-spacing: 1px;
}
.detalle-desc {
  font-size: 1.1rem;
  color: #333;
  font-weight: 600;
}
.detalle-medidas {
  font-size: 1.04rem;
  color: #555;
}
.detalle-sscc {
  font-size: 1.01rem;
  color: #888;
}
.detalle-c {
  gap: 6px;
  color: #444;
}
.detalle-prop {
  color: #444;
  font-size: 1.01rem;
}
.detalle-ubicacion-instrucciones {
  margin-top: 14px;
  background: #f8fbff;
  border-radius: 10px;
  padding: 10px 8px;
  font-size: 1.02rem;
  color: #0073a8;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0,139,204,0.05);
}
@media (max-width: 600px) {
  .detalle-ubicacion-wrapper {
    padding: 8px 2vw 20px 2vw;
    max-width: 100vw;
  }
  .detalle-ubicacion-header h3 {
    font-size: 1.05rem;
  }
  .detalle-ubicacion-btn-back {
    font-size: 0.95rem;
    padding: 7px 8px;
  }
  .detalle-ubicacion-input-box input {
    font-size: 1rem;
    padding: 10px 4vw;
    max-width: 96vw;
  }
  .detalle-ubicacion-card {
    font-size: 0.99rem;
    padding: 10px 3vw 8px 3vw;
  }
}
