# Lista de Tareas del Proyecto Almacén App

## ✅ Tareas Realizadas

### App Móvil (Flutter: `almacen_app`)
- [x] Estructura de carpetas y organización de proyecto Flutter _(Tiempo dedicado: 1 día)_
- [x] Implementación de Splash Screen nativo para Android (API 21+ y 31+) _(Tiempo dedicado: 1 día)_
- [x] Navegación principal con bottom bar e IndexedStack _(Tiempo dedicado: 2 días)_
  - [x] Sección Inventario _(0.5 días)_
  - [x] Sección Firma Digital _(0.5 días)_
  - [x] Sección Fotos _(0.5 días)_
  - [x] Sección Navegador Web integrado _(0.5 días)_
- [x] Implementación de la pantalla de Inventario (`inventory_screen`) _(1 día)_
- [x] Implementación de la pantalla de Firma Digital (`signature_screen`) _(1 día)_
  - [x] Selección de color y grosor de trazo _(0.5 días)_
  - [x] Exportación de firma como PNG _(0.5 días)_
  - [x] Vista previa de firma guardada _(0.5 días)_
- [x] Implementación de la pantalla de Fotos (`photo_screen`) _(1 día)_
- [x] Implementación de la pantalla principal (`home_screen`) _(0.5 días)_
- [x] Implementación de la pantalla de configuración (`settings_screen`) _(0.5 días)_
- [x] Implementación de la pantalla web (`web_screen`) que carga la web de WebAlmacen _(0.5 días)_
- [x] Gestión de modelos de datos (`models/item_model`) _(0.5 días)_
- [x] Implementación de servicios de configuración (`services/settings_service`) _(0.5 días)_
- [x] Widgets reutilizables _(1 día)_
- [x] Configuración de dependencias principales (`signature`, `image_picker`, `webview_flutter`, etc.) _(0.5 días)_
- [x] Soporte multiplataforma (Android/iOS/Linux/Mac/Windows) _(1 día)_
- [x] Scripts de automatización (`run_app.bat`, `run_web.bat`) _(0.5 días)_
- [x] Documentación técnica y solución de problemas comunes (`README.md`) _(0.5 días)_

### Web (`WebAlmacen`)
- [x] Estructura de proyecto React + Vite _(Tiempo dedicado: 1 día)_
- [x] Implementación de navegación y pantallas principales en React (`App.jsx`) _(Tiempo dedicado: 2 días)_
  - [x] Menú principal (`Menu.jsx`) _(0.5 días)_
  - [x] Pantalla de ubicación de mercancía (`UbicacionMercancia.jsx`) _(0.5 días)_
  - [x] Pantalla de detalle de ubicación (`DetalleUbicacion.jsx`) _(0.5 días)_
  - [x] Componente de tarjeta deslizable (`SwipeableCard.jsx`) _(0.5 días)_
  - [x] Componente de vista alterna (`ToggleView.jsx`) _(0.5 días)_
- [x] Estilos globales (`styles/global.css`) _(0.5 días)_
- [x] Simulación de datos (`data/fakeEntradas.js`) _(0.5 días)_
- [x] Uso de hooks personalizados (`hooks/useScrollboxConfig.js`) _(0.5 días)_
- [x] Configuración de Vite y scripts de desarrollo _(0.5 días)_
- [x] Herramienta para convertir INI a JSON (`tools/ini_to_json.js`) _(0.5 días)_
- [x] Implementación de configuración visual y funcional a partir de archivo INI clásico (flujo INI → JSON → Web) _(1 día)_
- [x] Documentación de flujo de configuración INI → JSON (`README_CAMBIOS_INI.md`) _(0.5 días)_

## Tareas Pendientes / Mejoras Sugeridas

### App Móvil
- [ ] Implementar todas las mecanicas de la app convencional en el proyecto de app móvil-WebAlmacen.
- [ ] Mejorar pruebas unitarias y de integración (`test/`)
- [ ] Internacionalización (soporte multi-idioma)
- [ ] Mejorar accesibilidad (a11y) en widgets y pantallas
- [ ] Implementar almacenamiento local seguro (si se requiere persistencia más allá de la sesión)
- [ ] Optimizar el rendimiento para dispositivos de gama baja
- [ ] Sincronización offline/online de datos (si aplica)
- [ ] Mejorar la gestión de errores y mostrar mensajes más amigables al usuario
- [ ] Agregar onboarding/guía de usuario para primeras ejecuciones
- [ ] Añadir soporte para notificaciones push (si es necesario)
- [ ] Integrar configuración visual y funcional a partir de archivo INI (flujo INI → JSON → App)

### Web (`WebAlmacen`)
- [ ] Mejorar cobertura de pruebas unitarias y de integración
- [ ] Mejorar documentación técnica y de usuario final
- [ ] Implementar autenticación/roles si la aplicación lo requiere
- [ ] Mejorar experiencia móvil/responsiva
- [ ] Optimizar carga de recursos y performance
- [ ] Agregar tests automáticos para el flujo INI → JSON → Web
- [ ] Mejorar manejo de errores en frontend (mensajes claros y logs)
- [ ] Sincronización en tiempo real (si aplica)
- [ ] Internacionalización (i18n) y accesibilidad web